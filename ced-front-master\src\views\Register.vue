<template>
  <div class="register-container">
    <div class="register-content">
      <!-- 品牌区域 -->
      <div class="brand-section">
        <el-tooltip content="校园快递代取系统" placement="right" :show-after="500">
          <div class="logo">
            <el-icon size="40"><UserFilled /></el-icon>
          </div>
        </el-tooltip>
        <h1 class="brand-title">用户注册</h1>
        <p class="brand-subtitle">加入校园快递代取系统，享受便捷服务</p>
      </div>

      <!-- 注册表单 -->
      <div class="form-section">
        <!-- 用户类型选择 -->
        <div class="user-type-selector">
          <el-tooltip content="学生注册 - 发布代取订单" placement="top" :show-after="300">
            <div 
              class="type-card" 
              :class="{ active: activeTab === 'student' }"
              @click="activeTab = 'student'"
            >
              <div class="type-icon student">
                <el-icon size="20"><User /></el-icon>
              </div>
              <div class="type-info">
                <h4>学生</h4>
                <p>发布代取订单</p>
              </div>
            </div>
          </el-tooltip>
          
          <el-tooltip content="代取员注册 - 接单赚取收入" placement="top" :show-after="300">
            <div 
              class="type-card" 
              :class="{ active: activeTab === 'courier' }"
              @click="activeTab = 'courier'"
            >
              <div class="type-icon courier">
                <el-icon size="20"><Van /></el-icon>
              </div>
              <div class="type-info">
                <h4>代取员</h4>
                <p>接单赚取收入</p>
              </div>
            </div>
          </el-tooltip>
        </div>

        <!-- 注册表单卡片 -->
        <el-card class="register-card">
          <!-- 学生注册表单 -->
          <div v-if="activeTab === 'student'" class="register-form-container">
            <div class="form-header">
              <el-tooltip content="学生用户" placement="top">
                <el-icon size="24" color="#2563eb"><User /></el-icon>
              </el-tooltip>
              <h3>学生注册</h3>
            </div>
            <el-form :model="studentForm" :rules="studentRules" ref="studentFormRef" class="register-form">
              <el-row :gutter="16">
                <el-col :span="12">
                  <el-form-item prop="studentId">
                    <el-input
                      v-model="studentForm.studentId"
                      placeholder="学号"
                      size="large"
                      clearable
                    >
                      <template #prefix>
                        <el-icon><User /></el-icon>
                      </template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item prop="name">
                    <el-input
                      v-model="studentForm.name"
                      placeholder="真实姓名"
                      size="large"
                      clearable
                    >
                      <template #prefix>
                        <el-icon><UserFilled /></el-icon>
                      </template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-row :gutter="16">
                <el-col :span="12">
                  <el-form-item prop="phone">
                    <el-input
                      v-model="studentForm.phone"
                      placeholder="手机号"
                      size="large"
                      clearable
                    >
                      <template #prefix>
                        <el-icon><Phone /></el-icon>
                      </template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item prop="campus">
                    <el-select v-model="studentForm.campus" placeholder="选择校区" size="large" style="width: 100%">
                      <el-option label="本部校区" value="main" />
                      <el-option label="南校区" value="south" />
                      <el-option label="北校区" value="north" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-row :gutter="16">
                <el-col :span="12">
                  <el-form-item prop="dormBuilding">
                    <el-input
                      v-model="studentForm.dormBuilding"
                      placeholder="宿舍楼号"
                      size="large"
                      clearable
                    >
                      <template #prefix>
                        <el-icon><OfficeBuilding /></el-icon>
                      </template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item prop="roomNumber">
                    <el-input
                      v-model="studentForm.roomNumber"
                      placeholder="房间号"
                      size="large"
                      clearable
                    >
                      <template #prefix>
                        <el-icon><House /></el-icon>
                      </template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-row :gutter="16">
                <el-col :span="12">
                  <el-form-item prop="password">
                    <el-input
                      v-model="studentForm.password"
                      type="password"
                      placeholder="设置密码"
                      size="large"
                      show-password
                      clearable
                    >
                      <template #prefix>
                        <el-icon><Lock /></el-icon>
                      </template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item prop="confirmPassword">
                    <el-input
                      v-model="studentForm.confirmPassword"
                      type="password"
                      placeholder="确认密码"
                      size="large"
                      show-password
                      clearable
                    >
                      <template #prefix>
                        <el-icon><Lock /></el-icon>
                      </template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-form-item>
                <el-button
                  type="primary"
                  @click="handleRegister('student')"
                  :loading="loading"
                  size="large"
                  class="register-btn"
                >
                  注册学生账号
                </el-button>
              </el-form-item>
            </el-form>
          </div>

          <!-- 代取员注册表单 -->
          <div v-if="activeTab === 'courier'" class="register-form-container">
            <div class="form-header">
              <el-tooltip content="代取员用户" placement="top">
                <el-icon size="24" color="#10b981"><Van /></el-icon>
              </el-tooltip>
              <h3>代取员注册</h3>
            </div>
            <el-form :model="courierForm" :rules="courierRules" ref="courierFormRef" class="register-form">
              <el-row :gutter="16">
                <el-col :span="12">
                  <el-form-item prop="name">
                    <el-input
                      v-model="courierForm.name"
                      placeholder="真实姓名"
                      size="large"
                      clearable
                    >
                      <template #prefix>
                        <el-icon><UserFilled /></el-icon>
                      </template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item prop="studentId">
                    <el-input
                      v-model="courierForm.studentId"
                      placeholder="学号"
                      size="large"
                      clearable
                    >
                      <template #prefix>
                        <el-icon><User /></el-icon>
                      </template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-row :gutter="16">
                <el-col :span="12">
                  <el-form-item prop="phone">
                    <el-input
                      v-model="courierForm.phone"
                      placeholder="手机号"
                      size="large"
                      clearable
                    >
                      <template #prefix>
                        <el-icon><Phone /></el-icon>
                      </template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item prop="idCard">
                    <el-input
                      v-model="courierForm.idCard"
                      placeholder="身份证号"
                      size="large"
                      clearable
                    >
                      <template #prefix>
                        <el-icon><CreditCard /></el-icon>
                      </template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-form-item prop="alipayAccount">
                <el-input
                  v-model="courierForm.alipayAccount"
                  placeholder="支付宝账号（用于收款）"
                  size="large"
                  clearable
                >
                  <template #prefix>
                    <el-icon><Money /></el-icon>
                  </template>
                </el-input>
              </el-form-item>
              
              <el-row :gutter="16">
                <el-col :span="12">
                  <el-form-item prop="password">
                    <el-input
                      v-model="courierForm.password"
                      type="password"
                      placeholder="设置密码"
                      size="large"
                      show-password
                      clearable
                    >
                      <template #prefix>
                        <el-icon><Lock /></el-icon>
                      </template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item prop="confirmPassword">
                    <el-input
                      v-model="courierForm.confirmPassword"
                      type="password"
                      placeholder="确认密码"
                      size="large"
                      show-password
                      clearable
                    >
                      <template #prefix>
                        <el-icon><Lock /></el-icon>
                      </template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-form-item>
                <el-button
                  type="success"
                  @click="handleRegister('courier')"
                  :loading="loading"
                  size="large"
                  class="register-btn"
                >
                  申请成为代取员
                </el-button>
              </el-form-item>
              
              <el-alert
                title="代取员需要管理员审核通过后才能接单"
                type="info"
                :closable="false"
                show-icon
                class="register-notice"
              />
            </el-form>
          </div>

          <div class="register-footer">
            <div class="footer-actions">
              <el-tooltip content="返回登录页面" placement="top">
                <el-button type="text" @click="$router.push('/login')" class="footer-btn">
                  <el-icon><ArrowLeft /></el-icon>
                  返回登录
                </el-button>
              </el-tooltip>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { 
  User, UserFilled, Phone, Lock, Van, CreditCard, Money, 
  OfficeBuilding, House, ArrowLeft 
} from '@element-plus/icons-vue'
import { registerApi } from '../request/loginApi.ts'

const router = useRouter()
const activeTab = ref('student')
const loading = ref(false)

const studentForm = reactive({
  studentId: '',
  name: '',
  phone: '',
  campus: '',
  dormBuilding: '',
  roomNumber: '',
  password: '',
  confirmPassword: ''
})

const courierForm = reactive({
  name: '',
  studentId: '',
  idCard: '',
  phone: '',
  alipayAccount: '',
  password: '',
  confirmPassword: ''
})

const validateConfirmPassword = (rule, value, callback) => {
  const form = activeTab.value === 'student' ? studentForm : courierForm
  if (value !== form.password) {
    callback(new Error('两次输入的密码不一致'))
  } else {
    callback()
  }
}

const studentRules = {
  studentId: [{ required: true, message: '请输入学号', trigger: 'blur' }],
  name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
  phone: [{ required: true, message: '请输入手机号', trigger: 'blur' }],
  campus: [{ required: true, message: '请选择校区', trigger: 'change' }],
  dormBuilding: [{ required: true, message: '请输入宿舍楼', trigger: 'blur' }],
  roomNumber: [{ required: true, message: '请输入房间号', trigger: 'blur' }],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
  confirmPassword: [{ validator: validateConfirmPassword, trigger: 'blur' }]
}

const courierRules = {
  name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
  studentId: [{ required: true, message: '请输入学号', trigger: 'blur' }],
  idCard: [{ required: true, message: '请输入身份证号', trigger: 'blur' }],
  phone: [{ required: true, message: '请输入手机号', trigger: 'blur' }],
  alipayAccount: [{ required: true, message: '请输入支付宝账号', trigger: 'blur' }],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
  confirmPassword: [{ validator: validateConfirmPassword, trigger: 'blur' }]
}

const studentFormRef = ref()
const courierFormRef = ref()

const handleRegister = async (userType) => {
  const formRef = userType === 'student' ? studentFormRef.value : courierFormRef.value
  const form = userType === 'student' ? studentForm : courierForm
  
  if (!formRef) return
  
  try {
    await formRef.validate()
    loading.value = true
    
    // 构建注册数据
    const registerData = {
      username: form.studentId, // 使用学号作为用户名
      realName: form.name,
      phone: form.phone,
      studentId: form.studentId,
      password: form.password,
      userType: userType === 'student' ? 1 : 2,
      email: form.email || '',
      idCard: form.idCard || ''
    }
    
    const result = await registerApi(registerData)
    
    if (result.code === 200) {
      if (userType === 'student') {
        ElMessage.success('学生账号注册成功，请登录')
      } else {
        ElMessage.success('代取员申请提交成功，请等待管理员审核')
      }
      router.push('/login')
    } else {
      ElMessage.error(result.msg || '注册失败')
    }
  } catch (error) {
    console.error('注册失败:', error)
    ElMessage.error('注册失败，请检查网络连接')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.register-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  position: relative;
  overflow: hidden;
}

.register-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="10" cy="60" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="90" cy="40" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.register-content {
  display: flex;
  align-items: center;
  gap: 60px;
  max-width: 1200px;
  width: 100%;
  position: relative;
  z-index: 1;
}

.brand-section {
  flex: 1;
  color: white;
  text-align: left;
}

.logo {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  backdrop-filter: blur(10px);
  margin-bottom: 24px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  cursor: pointer;
}

.logo:hover .el-icon {
  transform: scale(1.05);
  transition: transform 0.2s ease;
}

.brand-title {
  font-size: 48px;
  font-weight: 700;
  margin: 0 0 16px 0;
  line-height: 1.2;
  background: linear-gradient(135deg, #ffffff 0%, #e3f2fd 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.brand-subtitle {
  font-size: 18px;
  margin: 0;
  opacity: 0.9;
  font-weight: 300;
  line-height: 1.6;
}

.form-section {
  flex: 0 0 500px;
}

.user-type-selector {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
}

.type-card {
  flex: 1;
  padding: 16px;
  background: rgba(255, 255, 255, 0.8);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  text-align: center;
}

.type-card:hover {
  background: rgba(255, 255, 255, 0.9);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.type-card.active {
  background: rgba(255, 255, 255, 0.95);
  border-color: #2563eb;
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.2);
  transform: translateY(-2px);
}

.type-card:hover .type-icon {
  transform: scale(1.1);
  transition: transform 0.2s ease;
}

.type-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 12px;
  color: white;
}

.type-icon.student {
  background: linear-gradient(135deg, #2563eb, #3b82f6);
}

.type-icon.courier {
  background: linear-gradient(135deg, #10b981, #34d399);
}

.type-info h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
}

.type-info p {
  margin: 0;
  font-size: 12px;
  color: #6b7280;
}

.register-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  padding: 24px;
}

.register-card :deep(.el-card__body) {
  padding: 0;
}

.register-form-container {
  animation: fadeIn 0.3s ease-out;
}

.form-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 28px;
  padding-bottom: 20px;
  border-bottom: 1px solid #f1f5f9;
}

.form-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #1a1a1a;
}

.form-header .el-icon:hover {
  transform: scale(1.1);
  transition: transform 0.2s ease;
}

.register-form {
  padding: 8px 4px;
}

.register-form .el-form-item {
  margin-bottom: 24px;
}

.register-form .el-form-item:last-child {
  margin-bottom: 0;
}

/* 为行内的列添加底部间距 */
.register-form .el-row {
  margin-bottom: 8px;
}

.register-form .el-row:last-of-type {
  margin-bottom: 0;
}

.register-btn {
  width: 100%;
  height: 48px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 12px;
}

.register-notice {
  margin-top: 16px;
  border-radius: 8px;
}

.register-footer {
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid #f1f5f9;
}

.footer-actions {
  display: flex;
  justify-content: center;
}

.footer-btn {
  color: #64748b;
  font-weight: 500;
  font-size: 14px;
}

.footer-btn:hover {
  color: #2563eb;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Tooltip 自定义样式 */
:deep(.el-tooltip__popper) {
  background: rgba(0, 0, 0, 0.8) !important;
  border: none !important;
  border-radius: 8px !important;
  padding: 8px 12px !important;
  font-size: 12px !important;
  font-weight: 500 !important;
  backdrop-filter: blur(10px) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

:deep(.el-tooltip__popper .el-popper__arrow::before) {
  background: rgba(0, 0, 0, 0.8) !important;
  border: none !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .register-content {
    flex-direction: column;
    gap: 40px;
    text-align: center;
  }
  
  .brand-title {
    font-size: 36px;
  }
  
  .form-section {
    flex: none;
    width: 100%;
    max-width: 500px;
  }
  
  .user-type-selector {
    flex-direction: column;
    gap: 8px;
  }
  
  .type-card {
    display: flex;
    align-items: center;
    text-align: left;
    padding: 12px 16px;
  }
  
  .type-icon {
    width: 40px;
    height: 40px;
    margin: 0 12px 0 0;
  }
  
  .type-info h4 {
    font-size: 14px;
  }
  
  .type-info p {
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .register-container {
    padding: 16px;
  }
  
  .brand-title {
    font-size: 28px;
  }
  
  .brand-subtitle {
    font-size: 16px;
  }
  
  .form-section {
    max-width: 100%;
  }
  
  .register-form .el-col {
    margin-bottom: 16px;
  }
  
  .register-form .el-row {
    margin-bottom: 0;
  }
  
  .register-form .el-form-item {
    margin-bottom: 20px;
  }
}
</style>