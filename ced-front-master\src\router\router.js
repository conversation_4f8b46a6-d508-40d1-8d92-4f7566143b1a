import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '../stores/auth.js'

const routes = [
  {
    path: '/',
    redirect: '/login'
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/Login.vue')
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('../views/Register.vue')
  },
  // 学生端路由
  {
    path: '/student',
    component: () => import('../layouts/StudentLayout.vue'),
    meta: { requiresAuth: true, userType: 'student' },
    children: [
      {
        path: '',
        redirect: '/student/home'
      },
      {
        path: 'home',
        name: 'StudentHome',
        component: () => import('../views/student/Home.vue')
      },
      {
        path: 'create-order',
        name: 'CreateOrder',
        component: () => import('../views/student/CreateOrder.vue')
      },
      {
        path: 'orders',
        name: 'StudentOrders',
        component: () => import('../views/student/Orders.vue')
      },
      {
        path: 'profile',
        name: 'StudentProfile',
        component: () => import('../views/student/Profile.vue')
      },
      {
        path: 'notices',
        name: 'StudentNotices',
        component: () => import('../views/student/Notices.vue')
      }
    ]
  },
  // 代取员端路由
  {
    path: '/courier',
    component: () => import('../layouts/CourierLayout.vue'),
    meta: { requiresAuth: true, userType: 'courier' },
    children: [
      {
        path: '',
        redirect: '/courier/orders'
      },
      {
        path: 'orders',
        name: 'CourierOrders',
        component: () => import('../views/courier/Orders.vue')
      },
      {
        path: 'my-orders',
        name: 'CourierMyOrders',
        component: () => import('../views/courier/MyOrders.vue')
      },
      {
        path: 'earnings',
        name: 'CourierEarnings',
        component: () => import('../views/courier/Earnings.vue')
      },
      {
        path: 'profile',
        name: 'CourierProfile',
        component: () => import('../views/courier/Profile.vue')
      },
      {
        path: 'notices',
        name: 'CourierNotices',
        component: () => import('../views/courier/Notices.vue')
      }
    ]
  },
  // 管理员端路由
  {
    path: '/admin',
    component: () => import('../layouts/AdminLayout.vue'),
    meta: { requiresAuth: true, userType: 'admin' },
    children: [
      {
        path: '',
        redirect: '/admin/dashboard'
      },
      {
        path: 'dashboard',
        name: 'AdminDashboard',
        component: () => import('../views/admin/Dashboard.vue')
      },
      {
        path: 'users',
        name: 'AdminUsers',
        component: () => import('../views/admin/Users.vue')
      },
      {
        path: 'orders',
        name: 'AdminOrders',
        component: () => import('../views/admin/Orders.vue')
      },
      {
        path: 'couriers',
        name: 'AdminCouriers',
        component: () => import('../views/admin/Couriers.vue')
      },
      {
        path: 'notices',
        name: 'AdminNotices',
        component: () => import('../views/admin/Notices.vue')
      },
      {
        path: 'profile',
        name: 'AdminProfile',
        component: () => import('../views/admin/Profile.vue')
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()
  
  if (to.meta.requiresAuth) {
    if (!authStore.token) {
      next('/login')
      return
    }
    
    if (to.meta.userType && authStore.userType !== to.meta.userType) {
      // 重定向到对应用户类型的首页
      switch (authStore.userType) {
        case 'student':
          next('/student')
          break
        case 'courier':
          next('/courier')
          break
        case 'admin':
          next('/admin')
          break
        default:
          next('/login')
      }
      return
    }
  }
  
  next()
})

export default router