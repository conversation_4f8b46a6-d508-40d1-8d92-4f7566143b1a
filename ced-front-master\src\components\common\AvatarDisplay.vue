<template>
  <div class="avatar-display-container">
    <div 
      class="avatar-wrapper"
      :class="{ 'loading': loading, 'clickable': clickable }"
      :style="{ width: size + 'px', height: size + 'px' }"
      @click="handleClick"
    >
      <!-- 加载状态 -->
      <div v-if="loading" class="avatar-loading">
        <el-icon class="loading-icon"><Loading /></el-icon>
      </div>
      
      <!-- 头像显示 -->
      <img 
        v-else-if="displayUrl"
        :src="displayUrl" 
        :alt="alt"
        class="avatar-image"
        @error="handleImageError"
        @load="handleImageLoad"
      />
      
      <!-- 默认头像 -->
      <div v-else class="avatar-default">
        <el-icon class="default-icon"><User /></el-icon>
      </div>
      
      <!-- 悬停操作 -->
      <div v-if="showActions && !loading" class="avatar-actions">
        <el-button
          type="primary"
          :icon="ZoomIn"
          @click.stop="handlePreview"
          size="small"
          circle
          title="预览头像"
        />
      </div>
    </div>
    
    <!-- 头像预览对话框 -->
    <el-dialog
      v-model="previewVisible"
      title="头像预览"
      width="50%"
      :before-close="closePreview"
      append-to-body
    >
      <div class="avatar-preview-content">
        <img 
          :src="displayUrl" 
          :alt="alt" 
          class="avatar-preview-image"
        />
      </div>
      <template #footer>
        <div class="preview-footer">
          <el-button @click="closePreview">关闭</el-button>
          <el-button type="primary" @click="downloadAvatar">下载头像</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, watch, defineProps, defineEmits } from 'vue'
import { ElMessage } from 'element-plus'
import { Loading, User, ZoomIn } from '@element-plus/icons-vue'
import { useImageHandler } from '../../composables/useImageHandler.js'

const props = defineProps({
  avatarPath: {
    type: String,
    default: ''
  },
  alt: {
    type: String,
    default: '用户头像'
  },
  size: {
    type: Number,
    default: 80
  },
  showActions: {
    type: Boolean,
    default: true
  },
  clickable: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['click', 'load-success', 'load-error'])

const {
  loading,
  displayUrl,
  loadImage,
  clearDisplayUrl
} = useImageHandler()

const previewVisible = ref(false)
const hasError = ref(false)

// 监听头像路径变化
watch(() => props.avatarPath, async (newPath) => {
  if (newPath) {
    hasError.value = false
    try {
      await loadImage(newPath)
      emit('load-success', newPath)
    } catch (error) {
      hasError.value = true
      emit('load-error', error)
    }
  } else {
    clearDisplayUrl()
    hasError.value = false
  }
}, { immediate: true })

// 头像点击处理
const handleClick = () => {
  if (props.clickable && displayUrl.value) {
    emit('click', props.avatarPath)
    if (props.showActions) {
      handlePreview()
    }
  }
}

// 图片加载成功
const handleImageLoad = () => {
  hasError.value = false
  console.log('头像加载成功')
}

// 图片加载错误
const handleImageError = () => {
  hasError.value = true
  console.error('头像加载失败')
}

// 预览头像
const handlePreview = () => {
  if (displayUrl.value) {
    previewVisible.value = true
  } else {
    ElMessage.warning('没有可预览的头像')
  }
}

// 关闭预览
const closePreview = () => {
  previewVisible.value = false
}

// 下载头像
const downloadAvatar = () => {
  if (displayUrl.value && props.avatarPath) {
    try {
      const link = document.createElement('a')
      link.href = displayUrl.value
      link.download = `avatar_${Date.now()}.jpg`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      ElMessage.success('头像下载成功')
    } catch (error) {
      console.error('下载失败:', error)
      ElMessage.error('头像下载失败')
    }
  }
}
</script>

<style scoped>
.avatar-display-container {
  position: relative;
  display: inline-block;
}

.avatar-wrapper {
  position: relative;
  border-radius: 50%;
  overflow: hidden;
  background: #f5f7fa;
  border: 2px solid #e4e7ed;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-wrapper:hover {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.avatar-wrapper.clickable {
  cursor: pointer;
}

.avatar-wrapper.loading {
  border-color: #409eff;
}

.avatar-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  color: #409eff;
}

.loading-icon {
  font-size: 24px;
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.avatar-wrapper.clickable:hover .avatar-image {
  transform: scale(1.05);
}

.avatar-default {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.default-icon {
  font-size: 50%;
}

.avatar-actions {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.avatar-wrapper:hover .avatar-actions {
  opacity: 1;
}

.avatar-preview-content {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  background: #f5f7fa;
  border-radius: 8px;
  padding: 20px;
}

.avatar-preview-image {
  max-width: 100%;
  max-height: 60vh;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  background: white;
}

.preview-footer {
  display: flex;
  justify-content: center;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .avatar-actions {
    opacity: 1;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    padding: 4px;
  }
  
  .avatar-preview-image {
    max-height: 50vh;
  }
}
</style>