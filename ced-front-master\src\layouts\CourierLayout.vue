<template>
  <el-container class="layout-container">
    <el-header class="layout-header">
      <div class="header-left">
        <div class="logo-section">
          <el-icon size="32" color="white"><Van /></el-icon>
          <div class="title-section">
            <h3>校园快递代取系统</h3>
            <span class="user-type">代取员端</span>
          </div>
        </div>
      </div>
      <div class="header-right">
        <div class="user-info">
          <div class="user-details">
            <span class="welcome">{{ authStore.user?.realName || authStore.user?.username }}</span>
            <span class="user-role">代取员</span>
          </div>
          <el-dropdown @command="handleCommand" trigger="click">
            <el-avatar :size="36" :src="authStore.user?.avatar" class="user-avatar-dropdown">
              {{ (authStore.user?.realName || authStore.user?.username)?.charAt(0) }}
            </el-avatar>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">
                  <el-icon><User /></el-icon>
                  个人中心
                </el-dropdown-item>
                <el-dropdown-item divided command="logout">
                  <el-icon><SwitchButton /></el-icon>
                  退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </el-header>
    
    <el-container>
      <el-aside width="240px" class="layout-aside">
        <div class="sidebar-header">
          <div class="user-avatar">
            <el-avatar :size="60" :src="authStore.user?.avatar">
              {{ (authStore.user?.realName || authStore.user?.username)?.charAt(0) }}
            </el-avatar>
          </div>
          <div class="user-info-sidebar">
            <h4>{{ authStore.user?.realName || authStore.user?.username }}</h4>
            <p>工号：{{ authStore.user?.employeeId || 'C' + String(authStore.user?.id).padStart(4, '0') }}</p>
          </div>
        </div>
        
        <el-menu
          :default-active="$route.path"
          router
          class="sidebar-menu"
          :collapse="false"
        >
          <el-menu-item index="/courier/orders" class="menu-item">
            <el-icon><ShoppingBag /></el-icon>
            <span>订单大厅</span>
          </el-menu-item>
          <el-menu-item index="/courier/my-orders" class="menu-item">
            <el-icon><List /></el-icon>
            <span>我的订单</span>
          </el-menu-item>
          <el-menu-item index="/courier/earnings" class="menu-item">
            <el-icon><Money /></el-icon>
            <span>收入管理</span>
          </el-menu-item>
          <el-menu-item index="/courier/notices" class="menu-item">
            <el-icon><Bell /></el-icon>
            <span>系统公告</span>
          </el-menu-item>
          <el-menu-item index="/courier/profile" class="menu-item">
            <el-icon><User /></el-icon>
            <span>个人中心</span>
          </el-menu-item>
        </el-menu>
      </el-aside>
      
      <el-main class="layout-main">
        <router-view />
      </el-main>
    </el-container>
  </el-container>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth.js'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  ShoppingBag, List, Money, User, Bell, Van, ArrowDown, Setting, 
  SwitchButton 
} from '@element-plus/icons-vue'

const router = useRouter()
const authStore = useAuthStore()

const handleCommand = async (command) => {
  switch (command) {
    case 'profile':
      router.push('/courier/profile')
      break
    case 'settings':
      ElMessage.info('设置功能开发中')
      break
    case 'logout':
      await handleLogout()
      break
  }
}

const handleLogout = async () => {
  try {
    await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    authStore.logout()
    ElMessage.success('退出登录成功')
    router.push('/login')
  } catch {
    // 用户取消
  }
}
</script>

<style scoped>
.layout-container {
  height: 100vh;
  background: #fafbfc;
  overflow: hidden;
}

.layout-header {
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
  color: #1e293b;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1000;
  border-bottom: 1px solid #e2e8f0;
}

.header-left {
  display: flex;
  align-items: center;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-section h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.user-type {
  background: #d1fae5;
  color: #065f46;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  margin-top: 2px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-details {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.welcome {
  font-size: 14px;
  font-weight: 500;
  line-height: 1.2;
}

.user-role {
  font-size: 12px;
  opacity: 0.8;
  line-height: 1.2;
}

.user-avatar-dropdown {
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.user-avatar-dropdown:hover {
  border-color: #059669;
  transform: scale(1.05);
}

.layout-aside {
  background: white;
  border-right: 1px solid #e5e7eb;
  box-shadow: 4px 0 6px -1px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  padding: 24px 20px;
  text-align: center;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #e4e7ed;
}

.user-avatar {
  margin-bottom: 12px;
}

.user-info-sidebar h4 {
  margin: 0 0 4px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.user-info-sidebar p {
  margin: 0;
  color: #909399;
  font-size: 12px;
}

.sidebar-menu {
  border: none;
  background: transparent;
  flex: 1;
  padding: 12px 0;
}

.sidebar-menu .menu-item {
  margin: 4px 12px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.sidebar-menu .menu-item:hover {
  background: #f1f5f9;
  color: #059669;
  transform: translateX(2px);
}

.sidebar-menu .menu-item.is-active {
  background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
  color: #065f46;
  box-shadow: 0 2px 4px rgba(16, 185, 129, 0.15);
  transform: translateX(4px);
}

.sidebar-menu .menu-item.is-active:hover {
  background: linear-gradient(135deg, #a7f3d0 0%, #6ee7b7 100%);
  color: #047857;
}

.layout-main {
  background: #fafbfc;
  padding: 16px;
  overflow-y: auto;
  height: calc(100vh - 60px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .layout-aside {
    width: 200px !important;
  }
  
  .sidebar-header {
    padding: 16px;
  }
  
  .user-info-sidebar h4 {
    font-size: 14px;
  }
  
  .layout-main {
    padding: 16px;
  }
}
</style>