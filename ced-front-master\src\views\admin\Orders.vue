<template>
  <div class="orders-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <h3>订单管理</h3>
          <div class="header-actions">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索订单号/用户"
              style="width: 200px; margin-right: 10px;"
              clearable
            />
            <el-button type="primary" @click="searchOrders">搜索</el-button>
            <el-button @click="exportOrders">导出数据</el-button>
          </div>
        </div>
      </template>
      
      <div class="filter-bar">
        <el-row :gutter="20">
          <el-col :span="4">
            <el-select v-model="filters.status" placeholder="订单状态" clearable>
              <el-option label="待接单" :value="orderStore.ORDER_STATUS.PENDING" />
              <el-option label="已接单" :value="orderStore.ORDER_STATUS.ACCEPTED" />
              <el-option label="配送中" :value="orderStore.ORDER_STATUS.DELIVERING" />
              <el-option label="已完成" :value="orderStore.ORDER_STATUS.COMPLETED" />
              <el-option label="已取消" :value="orderStore.ORDER_STATUS.CANCELLED" />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-select v-model="filters.campus" placeholder="校区" clearable>
              <el-option label="本部校区" value="main" />
              <el-option label="南校区" value="south" />
              <el-option label="北校区" value="north" />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-date-picker
              v-model="filters.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              size="default"
            />
          </el-col>
          <el-col :span="4">
            <el-input-number
              v-model="filters.minFee"
              placeholder="最低费用"
              :min="0"
              :step="0.5"
              controls-position="right"
            />
          </el-col>
          <el-col :span="6">
            <el-button @click="applyFilters">筛选</el-button>
            <el-button @click="clearFilters">清空</el-button>
            <el-button type="danger" @click="batchDelete" :disabled="selectedOrders.length === 0">
              批量删除
            </el-button>
          </el-col>
        </el-row>
      </div>
      
      <el-table 
        :data="filteredOrders" 
        style="width: 100%" 
        v-loading="loading"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="订单号" width="120" />
        <el-table-column prop="userName" label="用户" width="100" />
        <el-table-column prop="expressPointName" label="快递点" width="150" />
        <el-table-column prop="pickupCode" label="取件码" width="120" />
        <el-table-column prop="deliveryAddress" label="送达地址" width="180" />
        <el-table-column prop="fee" label="费用" width="80">
          <template #default="scope">
            ¥{{ scope.row.fee }}
          </template>
        </el-table-column>
        <el-table-column prop="courierName" label="代取员" width="100">
          <template #default="scope">
            {{ scope.row.courierName || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180">
          <template #default="scope">
            {{ formatTime(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button type="primary" size="small" @click="viewOrderDetail(scope.row)">
              详情
            </el-button>
            <el-button 
              v-if="scope.row.status === orderStore.ORDER_STATUS.PENDING"
              type="warning" 
              size="small" 
              @click="assignCourier(scope.row)"
            >
              分配代取员
            </el-button>
            <el-button 
              v-if="canCancelOrder(scope.row.status)"
              type="danger" 
              size="small" 
              @click="cancelOrder(scope.row)"
            >
              取消订单
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="totalOrders"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    
    <!-- 订单详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="订单详情" width="700px">
      <div v-if="selectedOrder" class="order-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="订单号">{{ selectedOrder.id }}</el-descriptions-item>
          <el-descriptions-item label="用户">{{ selectedOrder.userName }}</el-descriptions-item>
          <el-descriptions-item label="联系方式">{{ selectedOrder.contactPhone }}</el-descriptions-item>
          <el-descriptions-item label="快递点">{{ selectedOrder.expressPointName }}</el-descriptions-item>
          <el-descriptions-item label="取件码">{{ selectedOrder.pickupCode }}</el-descriptions-item>
          <el-descriptions-item label="送达地址">{{ selectedOrder.deliveryAddress }}</el-descriptions-item>
          <el-descriptions-item label="代取费用">¥{{ selectedOrder.fee }}</el-descriptions-item>
          <el-descriptions-item label="代取员">{{ selectedOrder.courierName || '未分配' }}</el-descriptions-item>
          <el-descriptions-item label="期望送达时间">{{ formatTime(selectedOrder.expectedTime) }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatTime(selectedOrder.createTime) }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{ formatTime(selectedOrder.updateTime) }}</el-descriptions-item>
          <el-descriptions-item label="订单状态">
            <el-tag :type="getStatusType(selectedOrder.status)">
              {{ getStatusText(selectedOrder.status) }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
        
        <div v-if="selectedOrder.description" style="margin-top: 20px;">
          <h4>快递描述</h4>
          <p>{{ selectedOrder.description }}</p>
        </div>
        
        <div v-if="selectedOrder.specialRequirements" style="margin-top: 20px;">
          <h4>特殊要求</h4>
          <p>{{ selectedOrder.specialRequirements }}</p>
        </div>
        
        <div class="order-timeline" style="margin-top: 20px;">
          <h4>订单时间线</h4>
          <el-timeline>
            <el-timeline-item timestamp="订单创建" :timestamp="formatTime(selectedOrder.createTime)">
              用户创建订单
            </el-timeline-item>
            <el-timeline-item 
              v-if="selectedOrder.status !== orderStore.ORDER_STATUS.PENDING"
              timestamp="代取员接单" 
              :timestamp="formatTime(selectedOrder.updateTime)"
            >
              {{ selectedOrder.courierName }} 接单
            </el-timeline-item>
            <el-timeline-item 
              v-if="selectedOrder.status === orderStore.ORDER_STATUS.COMPLETED"
              timestamp="订单完成"
            >
              订单已完成
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
    </el-dialog>
    
    <!-- 分配代取员对话框 -->
    <el-dialog v-model="assignDialogVisible" title="分配代取员" width="500px">
      <el-form :model="assignForm" label-width="100px">
        <el-form-item label="选择代取员">
          <el-select v-model="assignForm.courierId" placeholder="请选择代取员" style="width: 100%">
            <el-option 
              v-for="courier in availableCouriers" 
              :key="courier.id" 
              :label="`${courier.name} (评分: ${courier.rating})`"
              :value="courier.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="assignForm.note" type="textarea" :rows="3" placeholder="可选" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="assignDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmAssign">确认分配</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useOrderStore } from '../../stores/order.js'
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'
import { adminApi } from '../../request/adminApi.js'

const orderStore = useOrderStore()

const loading = ref(false)
const searchKeyword = ref('')
const detailDialogVisible = ref(false)
const assignDialogVisible = ref(false)
const selectedOrder = ref(null)
const selectedOrders = ref([])
const currentPage = ref(1)
const pageSize = ref(20)
const totalCount = ref(0)

const filters = reactive({
  status: '',
  campus: '',
  dateRange: [],
  minFee: null
})

const assignForm = reactive({
  courierId: '',
  note: ''
})

// 可用代取员数据
const availableCouriers = ref([])

// 订单数据
const orders = ref([])

const filteredOrders = computed(() => {
  return orders.value
})

const totalOrders = computed(() => totalCount.value)

const formatTime = (time) => {
  if (!time) return ''
  return new Date(time).toLocaleString()
}

const getStatusType = (status) => {
  const statusMap = {
    [orderStore.ORDER_STATUS.PENDING]: 'warning',
    [orderStore.ORDER_STATUS.ACCEPTED]: 'primary',
    [orderStore.ORDER_STATUS.PICKED_UP]: 'primary',
    [orderStore.ORDER_STATUS.DELIVERING]: 'primary',
    [orderStore.ORDER_STATUS.DELIVERED]: 'success',
    [orderStore.ORDER_STATUS.COMPLETED]: 'success',
    [orderStore.ORDER_STATUS.CANCELLED]: 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    [orderStore.ORDER_STATUS.PENDING]: '待接单',
    [orderStore.ORDER_STATUS.ACCEPTED]: '已接单',
    [orderStore.ORDER_STATUS.PICKED_UP]: '已取件',
    [orderStore.ORDER_STATUS.DELIVERING]: '配送中',
    [orderStore.ORDER_STATUS.DELIVERED]: '已送达',
    [orderStore.ORDER_STATUS.COMPLETED]: '已完成',
    [orderStore.ORDER_STATUS.CANCELLED]: '已取消'
  }
  return statusMap[status] || '未知状态'
}

const canCancelOrder = (status) => {
  return [orderStore.ORDER_STATUS.PENDING, orderStore.ORDER_STATUS.ACCEPTED].includes(status)
}

// 搜索订单
const searchOrders = () => {
  currentPage.value = 1
  fetchOrders()
}

// 导出订单数据
const exportOrders = async () => {
  try {
    const loadingInstance = ElLoading.service({ text: '正在导出数据...' })
    
    // 构建导出参数
    const params = {
      keyword: searchKeyword.value || undefined,
      status: filters.status || undefined,
      campus: filters.campus || undefined,
      minFee: filters.minFee || undefined
    }
    
    // 添加日期筛选
    if (filters.dateRange && filters.dateRange.length === 2) {
      params.startDate = filters.dateRange[0].toISOString().split('T')[0]
      params.endDate = filters.dateRange[1].toISOString().split('T')[0]
    }
    
    const response = await adminApi.exportOrders(params)
    
    loadingInstance.close()
    
    // 处理导出的文件
    const blob = new Blob([response.data], { type: 'application/vnd.ms-excel' })
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = `订单数据_${new Date().toISOString().split('T')[0]}.xlsx`
    link.click()
    URL.revokeObjectURL(link.href)
    
    ElMessage.success('订单数据导出成功')
  } catch (error) {
    console.error('导出订单数据失败:', error)
    ElMessage.error('导出订单数据失败，请重试')
  }
}

// 应用筛选条件
const applyFilters = () => {
  currentPage.value = 1
  fetchOrders()
}

// 清空筛选条件
const clearFilters = () => {
  filters.status = ''
  filters.campus = ''
  filters.dateRange = []
  filters.minFee = null
  ElMessage.info('筛选条件已清空')
  currentPage.value = 1
  fetchOrders()
}

const handleSelectionChange = (selection) => {
  selectedOrders.value = selection
}

// 批量删除订单
const batchDelete = async () => {
  if (selectedOrders.value.length === 0) {
    ElMessage.warning('请选择要删除的订单')
    return
  }
  
  try {
    await ElMessageBox.confirm(`确定要删除选中的 ${selectedOrders.value.length} 个订单吗？`, '批量删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const loadingInstance = ElLoading.service({ text: '正在删除订单...' })
    
    try {
      const orderIds = selectedOrders.value.map(order => order.id)
      const response = await adminApi.batchDeleteOrders(orderIds)
      
      if (response.code === 200) {
        ElMessage.success('批量删除成功')
        fetchOrders() // 刷新列表
      } else {
        ElMessage.error(response.msg || '批量删除失败')
      }
    } catch (error) {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败，请重试')
    } finally {
      loadingInstance.close()
    }
  } catch {
    // 用户取消
  }
}

// 查看订单详情
const viewOrderDetail = async (order) => {
  const loadingInstance = ElLoading.service({ text: '加载订单详情...' })
  
  try {
    const response = await adminApi.getOrderDetail(order.id)
    
    if (response.code === 200) {
      selectedOrder.value = response.data
      detailDialogVisible.value = true
    } else {
      ElMessage.error(response.msg || '获取订单详情失败')
    }
  } catch (error) {
    console.error('获取订单详情失败:', error)
    ElMessage.error('获取订单详情失败，请重试')
  } finally {
    loadingInstance.close()
  }
}

// 分配代取员
const assignCourier = async (order) => {
  // 先获取可用代取员列表
  const loadingInstance = ElLoading.service({ text: '加载可用代取员...' })
  
  try {
    // 使用之前在Couriers.vue中定义的获取已通过审核的代取员接口
    const response = await adminApi.getApprovedCouriers()
    
    if (response.code === 200) {
      availableCouriers.value = response.data.records.map(courier => ({
        id: courier.id,
        name: courier.name,
        rating: courier.rating
      }))
      
      selectedOrder.value = order
      assignForm.courierId = ''
      assignForm.note = ''
      assignDialogVisible.value = true
    } else {
      ElMessage.error(response.msg || '获取可用代取员失败')
    }
  } catch (error) {
    console.error('获取可用代取员失败:', error)
    ElMessage.error('获取可用代取员失败，请重试')
  } finally {
    loadingInstance.close()
  }
}

// 确认分配代取员
const confirmAssign = async () => {
  if (!assignForm.courierId) {
    ElMessage.warning('请选择代取员')
    return
  }
  
  const loadingInstance = ElLoading.service({ text: '正在分配代取员...' })
  
  try {
    const response = await adminApi.assignCourier(
      selectedOrder.value.id,
      assignForm.courierId,
      assignForm.note
    )
    
    if (response.code === 200) {
      ElMessage.success('代取员分配成功')
      fetchOrders() // 刷新列表
      assignDialogVisible.value = false
    } else {
      ElMessage.error(response.msg || '代取员分配失败')
    }
  } catch (error) {
    console.error('代取员分配失败:', error)
    ElMessage.error('代取员分配失败，请重试')
  } finally {
    loadingInstance.close()
  }
}

// 取消订单
const cancelOrder = async (order) => {
  try {
    const { value: reason } = await ElMessageBox.prompt('请输入取消原因', '取消订单', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputPlaceholder: '请输入取消原因',
      type: 'warning'
    })
    
    const loadingInstance = ElLoading.service({ text: '正在取消订单...' })
    
    try {
      const response = await adminApi.cancelOrder(order.id, reason)
      
      if (response.code === 200) {
        ElMessage.success('订单已取消')
        fetchOrders() // 刷新列表
      } else {
        ElMessage.error(response.msg || '取消订单失败')
      }
    } catch (error) {
      console.error('取消订单失败:', error)
      ElMessage.error('取消订单失败，请重试')
    } finally {
      loadingInstance.close()
    }
  } catch {
    // 用户取消
  }
}

const handleSizeChange = (size) => {
  pageSize.value = size
  fetchOrders()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  fetchOrders()
}

// 获取订单列表
const fetchOrders = async () => {
  loading.value = true
  
  try {
    // 构建查询参数
    const params = {
      current: currentPage.value,
      size: pageSize.value,
      keyword: searchKeyword.value || undefined,
      status: filters.status || undefined,
      campus: filters.campus || undefined,
      minFee: filters.minFee || undefined
    }
    
    // 添加日期筛选
    if (filters.dateRange && filters.dateRange.length === 2) {
      params.startDate = filters.dateRange[0].toISOString().split('T')[0]
      params.endDate = filters.dateRange[1].toISOString().split('T')[0]
    }
    
    const response = await adminApi.getOrders(params)
    
    if (response.code === 200) {
      orders.value = response.data.records
      totalCount.value = response.data.total
    } else {
      ElMessage.error(response.msg || '获取订单列表失败')
    }
  } catch (error) {
    console.error('获取订单列表失败:', error)
    ElMessage.error('获取订单列表失败，请重试')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  // 初始化数据
  fetchOrders()
})
</script>

<style scoped>
.orders-container {
  width: 100%;
  height: 100%;
  padding: 8px;
  overflow-y: auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: #303133;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.filter-bar {
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.order-detail {
  max-height: 600px;
  overflow-y: auto;
}

.order-timeline h4 {
  margin-bottom: 15px;
  color: #303133;
}
</style>