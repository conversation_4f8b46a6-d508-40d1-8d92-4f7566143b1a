import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useOrderStore = defineStore('order', () => {
  const orders = ref([])
  const currentOrder = ref(null)

  // 订单状态
  const ORDER_STATUS = {
    PENDING: 'pending', // 待接单
    ACCEPTED: 'accepted', // 已接单
    PICKED_UP: 'picked_up', // 已取件
    DELIVERING: 'delivering', // 配送中
    DELIVERED: 'delivered', // 已送达
    COMPLETED: 'completed', // 已完成
    CANCELLED: 'cancelled' // 已取消
  }

  const createOrder = (orderData) => {
    const newOrder = {
      id: Date.now().toString(),
      ...orderData,
      status: ORDER_STATUS.PENDING,
      createTime: new Date(),
      updateTime: new Date()
    }
    orders.value.unshift(newOrder)
    return newOrder
  }

  const updateOrderStatus = (orderId, status) => {
    const order = orders.value.find(o => o.id === orderId)
    if (order) {
      order.status = status
      order.updateTime = new Date()
    }
  }

  const getOrdersByUser = (userId) => {
    return orders.value.filter(order => order.userId === userId)
  }

  const getOrdersByCourier = (courierId) => {
    return orders.value.filter(order => order.courierId === courierId)
  }

  const getPendingOrders = () => {
    return orders.value.filter(order => order.status === ORDER_STATUS.PENDING)
  }

  return {
    orders,
    currentOrder,
    ORDER_STATUS,
    createOrder,
    updateOrderStatus,
    getOrdersByUser,
    getOrdersByCourier,
    getPendingOrders
  }
})