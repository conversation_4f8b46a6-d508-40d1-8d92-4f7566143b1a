<template>
  <div class="create-order-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-background"></div>
      <div class="header-content">
        <div class="header-icon">
          <el-icon size="40"><Box /></el-icon>
        </div>
        <div class="header-text">
          <h2>发布代取订单</h2>
          <p>请填写详细的取件信息，我们会为您匹配合适的代取员</p>
        </div>
        <div class="header-stats">
          <div class="stat-item">
            <span class="stat-number">2-5</span>
            <span class="stat-label">分钟接单</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">30</span>
            <span class="stat-label">分钟送达</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">99%</span>
            <span class="stat-label">成功率</span>
          </div>
        </div>
      </div>
    </div>

    <el-form :model="orderForm" :rules="rules" ref="orderFormRef" label-width="120px">
      <div class="form-layout">
        <!-- 左侧表单区域 -->
        <div class="form-left">
          <el-card class="form-section-card" shadow="hover">
            <template #header>
              <div class="section-header">
                <div class="section-icon primary">
                  <el-icon size="18"><Box /></el-icon>
                </div>
                <div class="section-title">
                  <h3>基本信息</h3>
                </div>
              </div>
            </template>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="快递服务点" prop="expressPoint">
                <el-select v-model="orderForm.expressPoint" placeholder="请选择快递服务点" style="width: 100%">
                  <el-option label="本部菜鸟驿站" value="main_cainiao">
                    <div class="option-item">
                      <span class="option-name">本部菜鸟驿站</span>
                      <span class="option-desc">营业时间：8:00-22:00</span>
                    </div>
                  </el-option>
                  <el-option label="本部京东站点" value="main_jd">
                    <div class="option-item">
                      <span class="option-name">本部京东站点</span>
                      <span class="option-desc">营业时间：9:00-21:00</span>
                    </div>
                  </el-option>
                  <el-option label="本部顺丰站点" value="main_sf">
                    <div class="option-item">
                      <span class="option-name">本部顺丰站点</span>
                      <span class="option-desc">营业时间：8:30-21:30</span>
                    </div>
                  </el-option>
                  <el-option label="南校区菜鸟驿站" value="south_cainiao">
                    <div class="option-item">
                      <span class="option-name">南校区菜鸟驿站</span>
                      <span class="option-desc">营业时间：8:00-22:00</span>
                    </div>
                  </el-option>
                  <el-option label="南校区京东站点" value="south_jd">
                    <div class="option-item">
                      <span class="option-name">南校区京东站点</span>
                      <span class="option-desc">营业时间：9:00-21:00</span>
                    </div>
                  </el-option>
                  <el-option label="北校区菜鸟驿站" value="north_cainiao">
                    <div class="option-item">
                      <span class="option-name">北校区菜鸟驿站</span>
                      <span class="option-desc">营业时间：8:00-22:00</span>
                    </div>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            
            <el-col :span="12">
              <el-form-item label="取件码/运单号" prop="pickupCode">
                <el-input v-model="orderForm.pickupCode" placeholder="请输入取件码或运单号">
                  <template #prefix>
                    <el-icon><Key /></el-icon>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="期望送达时间" prop="expectedTime">
                <el-date-picker
                  v-model="orderForm.expectedTime"
                  type="datetime"
                  placeholder="选择期望送达时间"
                  style="width: 100%"
                  :disabled-date="disabledDate"
                />
              </el-form-item>
            </el-col>
            
            <el-col :span="12">
              <el-form-item label="代取费用" prop="fee">
                <div class="fee-input-wrapper">
                  <el-input-number
                    v-model="orderForm.fee"
                    :min="2"
                    :max="50"
                    :step="0.5"
                    placeholder="建议2-10元"
                    style="width: 100%"
                  />
                  <div class="fee-suggestions">
                    <el-button size="small" @click="orderForm.fee = 3" :type="orderForm.fee === 3 ? 'primary' : ''">¥3</el-button>
                    <el-button size="small" @click="orderForm.fee = 5" :type="orderForm.fee === 5 ? 'primary' : ''">¥5</el-button>
                    <el-button size="small" @click="orderForm.fee = 8" :type="orderForm.fee === 8 ? 'primary' : ''">¥8</el-button>
                    <el-button size="small" @click="orderForm.fee = 10" :type="orderForm.fee === 10 ? 'primary' : ''">¥10</el-button>
                  </div>
                </div>
                <div class="fee-tip">
                  <el-icon><InfoFilled /></el-icon>
                  建议费用：2-10元，费用越高越容易被接单
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-form-item label="快递描述">
            <el-input
              v-model="orderForm.description"
              type="textarea"
              :rows="2"
              placeholder="请描述快递的大小、颜色、特殊要求等（选填）"
            />
          </el-form-item>
          </el-card>

          <el-card class="form-section-card" shadow="hover">
            <template #header>
              <div class="section-header">
                <div class="section-icon success">
                  <el-icon size="18"><Location /></el-icon>
                </div>
                <div class="section-title">
                  <h3>配送信息</h3>
                </div>
              </div>
            </template>
          <el-form-item label="送达地址" prop="deliveryAddress">
            <el-row :gutter="10">
              <el-col :span="8">
                <el-select v-model="orderForm.campus" placeholder="校区">
                  <el-option label="本部校区" value="main" />
                  <el-option label="南校区" value="south" />
                  <el-option label="北校区" value="north" />
                </el-select>
              </el-col>
              <el-col :span="8">
                <el-input v-model="orderForm.dormBuilding" placeholder="宿舍楼号">
                  <template #prefix>
                    <el-icon><OfficeBuilding /></el-icon>
                  </template>
                </el-input>
              </el-col>
              <el-col :span="8">
                <el-input v-model="orderForm.roomNumber" placeholder="房间号">
                  <template #prefix>
                    <el-icon><House /></el-icon>
                  </template>
                </el-input>
              </el-col>
            </el-row>
          </el-form-item>
          
          <el-form-item label="联系方式" prop="contactPhone">
            <el-input v-model="orderForm.contactPhone" placeholder="请输入手机号">
              <template #prefix>
                <el-icon><Phone /></el-icon>
              </template>
            </el-input>
          </el-form-item>
          
          <el-form-item label="特殊要求">
            <el-input
              v-model="orderForm.specialRequirements"
              type="textarea"
              :rows="2"
              placeholder="如：送到楼下即可、需要上楼等（选填）"
            />
          </el-form-item>
          </el-card>
        </div>
        
        <!-- 右侧摘要区域 -->
        <div class="form-right">
          <el-card class="summary-section-card" shadow="hover">
            <template #header>
              <div class="section-header">
                <div class="section-icon warning">
                  <el-icon size="18"><Check /></el-icon>
                </div>
                <div class="section-title">
                  <h3>订单摘要</h3>
                </div>
              </div>
            </template>
        
            <div class="summary-content">
              <div class="summary-list">
                <div class="summary-item">
                  <div class="summary-icon">
                    <el-icon><Shop /></el-icon>
                  </div>
                  <div class="summary-info">
                    <span class="label">快递服务点</span>
                    <span class="value">{{ getExpressPointName(orderForm.expressPoint) || '请选择服务点' }}</span>
                  </div>
                </div>
                
                <div class="summary-item">
                  <div class="summary-icon">
                    <el-icon><Key /></el-icon>
                  </div>
                  <div class="summary-info">
                    <span class="label">取件码</span>
                    <span class="value">{{ orderForm.pickupCode || '请输入取件码' }}</span>
                  </div>
                </div>
                
                <div class="summary-item">
                  <div class="summary-icon">
                    <el-icon><Clock /></el-icon>
                  </div>
                  <div class="summary-info">
                    <span class="label">期望送达</span>
                    <span class="value">{{ orderForm.expectedTime ? new Date(orderForm.expectedTime).toLocaleString() : '请选择时间' }}</span>
                  </div>
                </div>
                
                <div class="summary-item">
                  <div class="summary-icon">
                    <el-icon><Location /></el-icon>
                  </div>
                  <div class="summary-info">
                    <span class="label">送达地址</span>
                    <span class="value">{{ getFullAddress() || '请填写地址' }}</span>
                  </div>
                </div>
              </div>
              
              <el-divider />
              
              <div class="total-section">
                <div class="total-item">
                  <span class="total-label">代取费用</span>
                  <span class="total-amount">¥{{ orderForm.fee }}</span>
                </div>
                <div class="total-description">
                  费用将在订单完成后支付给代取员
                </div>
              </div>
            </div>
          </el-card>
          
          <!-- 操作按钮 -->
          <div class="form-actions">
            <el-button size="large" @click="handleReset" class="reset-btn">
              <el-icon><Refresh /></el-icon>
              重置表单
            </el-button>
            <el-button type="primary" @click="handleSubmit" :loading="loading" size="large" class="submit-btn">
              <el-icon v-if="!loading"><Money /></el-icon>
              发布订单（支付¥{{ orderForm.fee }}）
            </el-button>
          </div>
        </div>
      </div>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useOrderStore } from '../../stores/order.js'
import { useAuthStore } from '../../stores/auth.js'
import { ElMessage } from 'element-plus'
import { 
  EditPen, Check, Money, Box, Refresh, Key, Location, 
  InfoFilled, OfficeBuilding, House, Phone, Shop, Clock
} from '@element-plus/icons-vue'

const router = useRouter()
const orderStore = useOrderStore()
const authStore = useAuthStore()

const loading = ref(false)
const orderFormRef = ref()
const currentStep = ref(0)

const orderForm = reactive({
  expressPoint: '',
  pickupCode: '',
  expectedTime: '',
  fee: 5,
  description: '',
  campus: '',
  dormBuilding: '',
  roomNumber: '',
  contactPhone: '',
  specialRequirements: ''
})

const rules = {
  expressPoint: [{ required: true, message: '请选择快递服务点', trigger: 'change' }],
  pickupCode: [{ required: true, message: '请输入取件码或运单号', trigger: 'blur' }],
  expectedTime: [{ required: true, message: '请选择期望送达时间', trigger: 'change' }],
  fee: [{ required: true, message: '请设置代取费用', trigger: 'blur' }],
  campus: [{ required: true, message: '请选择校区', trigger: 'change' }],
  dormBuilding: [{ required: true, message: '请输入宿舍楼号', trigger: 'blur' }],
  roomNumber: [{ required: true, message: '请输入房间号', trigger: 'blur' }],
  contactPhone: [{ required: true, message: '请输入联系方式', trigger: 'blur' }]
}

const disabledDate = (time) => {
  return time.getTime() < Date.now() - 8.64e7 // 不能选择过去的日期
}

const getExpressPointName = (value) => {
  const pointMap = {
    'main_cainiao': '本部菜鸟驿站',
    'main_jd': '本部京东站点',
    'main_sf': '本部顺丰站点',
    'south_cainiao': '南校区菜鸟驿站',
    'south_jd': '南校区京东站点',
    'north_cainiao': '北校区菜鸟驿站'
  }
  return pointMap[value] || value
}

const getFullAddress = () => {
  const campusMap = {
    'main': '本部校区',
    'south': '南校区',
    'north': '北校区'
  }
  
  if (!orderForm.campus) return '未填写'
  
  return `${campusMap[orderForm.campus]} ${orderForm.dormBuilding} ${orderForm.roomNumber}`
}

const handleSubmit = async () => {
  if (!orderFormRef.value) return
  
  try {
    await orderFormRef.value.validate()
    currentStep.value = 1
    loading.value = true
    
    // 模拟支付过程
    await new Promise(resolve => setTimeout(resolve, 1000))
    currentStep.value = 2
    
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const orderData = {
      ...orderForm,
      userId: authStore.user.id,
      userName: authStore.user.name,
      deliveryAddress: getFullAddress(),
      expressPointName: getExpressPointName(orderForm.expressPoint)
    }
    
    const newOrder = orderStore.createOrder(orderData)
    
    ElMessage.success('订单发布成功，等待代取员接单')
    router.push('/student/orders')
  } catch (error) {
    console.error('发布订单失败:', error)
    currentStep.value = 0
  } finally {
    loading.value = false
  }
}

const handleReset = () => {
  orderFormRef.value?.resetFields()
}

onMounted(() => {
  // 初始化用户地址信息
  if (authStore.user) {
    orderForm.campus = authStore.user.campus || ''
    orderForm.dormBuilding = authStore.user.dormBuilding || ''
    orderForm.roomNumber = authStore.user.roomNumber || ''
    orderForm.contactPhone = authStore.user.phone || ''
  }
})
</script>

<style scoped>
.create-order-container {
  width: 100%;
  height: 100vh;
  padding: 16px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 16px;
  color: white;
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
}

.header-background {
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
  animation: rotate 20s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.header-content {
  display: flex;
  align-items: center;
  gap: 24px;
  position: relative;
  z-index: 1;
}

.header-icon {
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  flex-shrink: 0;
}

.header-text {
  flex: 1;
}

.header-text h2 {
  margin: 0 0 4px 0;
  font-size: 24px;
  font-weight: 700;
}

.header-text p {
  margin: 0;
  font-size: 14px;
  opacity: 0.9;
}

.header-stats {
  display: flex;
  gap: 20px;
  flex-shrink: 0;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 18px;
  font-weight: 700;
  line-height: 1;
}

.stat-label {
  display: block;
  font-size: 11px;
  opacity: 0.8;
  margin-top: 2px;
}

.form-layout {
  display: flex;
  gap: 20px;
  flex: 1;
  overflow: hidden;
}

.form-left {
  flex: 2;
  display: flex;
  flex-direction: column;
  gap: 16px;
  overflow-y: auto;
  padding-right: 8px;
}

.form-right {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-section-card, .summary-section-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 12px;
}

.section-icon {
  width: 36px;
  height: 36px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.section-icon.primary {
  background: linear-gradient(135deg, #2563eb, #3b82f6);
}

.section-icon.success {
  background: linear-gradient(135deg, #10b981, #34d399);
}

.section-icon.warning {
  background: linear-gradient(135deg, #f59e0b, #fbbf24);
}

.section-title h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
}

.form-section-card :deep(.el-form-item) {
  margin-bottom: 18px;
}

.form-section-card :deep(.el-form-item__label) {
  font-weight: 600;
  color: #303133;
  font-size: 14px;
}

.form-section-card :deep(.el-input) {
  border-radius: 6px;
}

.form-section-card :deep(.el-select) {
  width: 100%;
}

.form-section-card :deep(.el-date-picker) {
  width: 100%;
}

.form-section-card :deep(.el-input-number) {
  width: 100%;
}

.option-item {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.option-name {
  font-weight: 500;
}

.option-desc {
  font-size: 12px;
  color: #909399;
}

.fee-input-wrapper {
  position: relative;
}

.fee-suggestions {
  display: flex;
  gap: 8px;
  margin-top: 8px;
}

.fee-suggestions .el-button {
  border-radius: 16px;
  font-size: 12px;
}

.fee-tip {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #909399;
  margin-top: 12px;
  padding: 12px 16px;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 8px;
  border-left: 3px solid #409eff;
}

.summary-content {
  padding: 4px 0;
}

.summary-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.summary-icon {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #dbeafe, #bfdbfe);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #2563eb;
  flex-shrink: 0;
}

.summary-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.summary-info .label {
  font-size: 11px;
  color: #64748b;
  font-weight: 500;
}

.summary-info .value {
  font-size: 13px;
  color: #1e293b;
  font-weight: 600;
  word-break: break-all;
}

.total-section {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border-radius: 8px;
  padding: 16px;
  text-align: center;
}

.total-item {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  margin-bottom: 6px;
}

.total-label {
  font-size: 14px;
  color: #92400e;
  font-weight: 600;
}

.total-amount {
  font-size: 24px;
  color: #f59e0b;
  font-weight: 700;
}

.total-description {
  font-size: 11px;
  color: #92400e;
  opacity: 0.8;
}

.form-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 16px;
}

.reset-btn {
  width: 100%;
  height: 40px;
  font-size: 14px;
  font-weight: 500;
  border-radius: 20px;
  border: 2px solid #e5e7eb;
  background: white;
  color: #6b7280;
  transition: all 0.3s ease;
}

.reset-btn:hover {
  border-color: #d1d5db;
  background: #f9fafb;
}

.submit-btn {
  width: 100%;
  height: 44px;
  font-size: 15px;
  font-weight: 600;
  border-radius: 22px;
  background: linear-gradient(135deg, #2563eb, #3b82f6);
  border: none;
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
  transition: all 0.3s ease;
}

.submit-btn:hover {
  background: linear-gradient(135deg, #1d4ed8, #2563eb);
  box-shadow: 0 6px 16px rgba(37, 99, 235, 0.4);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .create-order-container {
    padding: 12px;
  }
  
  .header-content {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }
  
  .header-stats {
    justify-content: center;
  }
  
  .form-layout {
    flex-direction: column;
    overflow-y: auto;
  }
  
  .form-left, .form-right {
    flex: none;
    overflow: visible;
  }
  
  .form-left {
    padding-right: 0;
  }
}
</style>