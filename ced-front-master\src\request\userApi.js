import request from './request'

// 用户相关API
export const userApi = {
  // 获取用户详细资料
  getUserProfile() {
    return request.get('/user/profile')
  },

  // 更新用户基本信息
  updateUserProfile(data) {
    return request.put('/user/profile', data)
  },

  // 修改密码
  updatePassword(data) {
    return request.put('/user/password', data)
  },

  // 上传头像
  uploadAvatar: (file) => {
    const formData = new FormData()
    formData.append('file', file)
    return request.post('/user/avatar', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 删除头像
  deleteAvatar() {
    return request.delete('/user/avatar')
  },

  // 获取用户统计信息
  getUserStatistics() {
    return request.get('/user/statistics')
  },

  // 获取用户地址列表
  getUserAddresses() {
    return request.get('/user/addresses')
  },

  // 获取代取员接单设置
  getPickupSettings() {
    return request.get('/user/pickup-settings')
  },

  // 更新代取员接单设置
  updatePickupSettings(data) {
    return request.put('/user/pickup-settings', data)
  }
}

// 钱包相关API
export const walletApi = {
  // 获取用户余额
  getBalance() {
    return request.get('/wallet/balance')
  },

  // 获取交易记录
  getTransactions(page = 1, size = 10) {
    return request.get('/wallet/transactions', {
      params: { page, size }
    })
  },

  // 充值
  recharge(data) {
    return request.post('/wallet/recharge', data)
  },

  // 提现
  withdraw(amount) {
    return request.post('/wallet/withdraw', null, {
      params: { amount }
    })
  }
}

// 通知相关API
export const notificationApi = {
  // 获取通知列表
  getNotifications(page = 1, size = 10, noticeType = null) {
    return request.get('/notification/list', {
      params: { page, size, noticeType }
    })
  },

  // 获取未读通知数量
  getUnreadCount() {
    return request.get('/notification/unread-count')
  },

  // 标记通知为已读
  markAsRead(noticeId) {
    return request.put(`/notification/${noticeId}/read`)
  },

  // 标记所有通知为已读
  markAllAsRead() {
    return request.put('/notification/read-all')
  },

  // 删除通知
  deleteNotification(noticeId) {
    return request.delete(`/notification/${noticeId}`)
  }
}