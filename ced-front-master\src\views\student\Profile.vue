<template>
  <div class="profile-container">
    <el-card>
      <template #header>
        <h3>个人中心</h3>
      </template>
      
      <el-tabs v-model="activeTab">
        <el-tab-pane label="个人信息" name="info">
          <el-form :model="userInfo" label-width="100px" class="profile-form">
            <el-form-item label="头像">
              <div class="avatar-section">
                <AvatarUpload
                  v-model="userInfo.avatar"
                  :size="120"
                  :max-size="2"
                  :allowed-types="['image/jpeg', 'image/png']"
                  upload-path="/avatar"
                  alt="用户头像"
                  :show-actions="false"
                  :show-delete="true"
                  :auto-upload="true"
                  @upload-success="handleAvatarSuccess"
                  @upload-error="handleAvatarError"
                  @delete-success="handleAvatarDelete"
                  @preview="handleAvatarPreview"
                />
                <div class="avatar-tips">
                  <p>支持 JPG、PNG 格式</p>
                  <p>建议尺寸：200x200像素</p>
                  <p>文件大小不超过 2MB</p>
                  <p>点击头像可预览、更换和下载</p>
                </div>
              </div>
            </el-form-item>
            <el-form-item label="姓名">
              <el-input v-model="userInfo.name" />
            </el-form-item>
            <el-form-item label="学号">
              <el-input v-model="userInfo.studentId" disabled />
            </el-form-item>
            <el-form-item label="手机号">
              <el-input v-model="userInfo.phone" />
            </el-form-item>
            <el-form-item label="校区">
              <el-select v-model="userInfo.campus">
                <el-option label="本部校区" value="main" />
                <el-option label="南校区" value="south" />
                <el-option label="北校区" value="north" />
              </el-select>
            </el-form-item>
            <el-form-item label="宿舍楼">
              <el-input v-model="userInfo.dormBuilding" />
            </el-form-item>
            <el-form-item label="房间号">
              <el-input v-model="userInfo.roomNumber" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="updateProfile">保存修改</el-button>
              <el-button @click="showPasswordDialog = true">修改密码</el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        
        <el-tab-pane label="钱包余额" name="wallet">
          <div class="wallet-section">
            <div class="balance-card">
              <h4>账户余额</h4>
              <div class="balance-amount">¥{{ wallet.balance }}</div>
              <el-button type="primary" @click="showRechargeDialog = true">充值</el-button>
            </div>
            
            <div class="transaction-history">
              <h4>交易记录</h4>
              <el-table :data="wallet.transactions" style="width: 100%" v-loading="loading">
                <el-table-column prop="time" label="时间" width="180" />
                <el-table-column prop="type" label="类型" width="100">
                  <template #default="scope">
                    <el-tag :type="getTagType(scope.row.type)">
                      {{ getTransactionTypeText(scope.row.type) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="amount" label="金额" width="100">
                  <template #default="scope">
                    <span :class="getAmountClass(scope.row.type)">
                      {{ getAmountPrefix(scope.row.type) }}¥{{ scope.row.amount }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column prop="description" label="描述" />
              </el-table>
              
              <div class="pagination-container" v-if="wallet.transactions.length > 0">
                <el-pagination
                  background
                  layout="prev, pager, next"
                  :total="wallet.total || 0"
                  :page-size="10"
                  @current-change="handleTransactionPageChange"
                />
              </div>
            </div>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="系统通知" name="notifications">
          <div class="notifications-section" v-loading="loading">
            <div v-if="notifications.length === 0" class="empty-notifications">
              <el-empty description="暂无通知" />
            </div>
            <div v-else>
              <div v-for="notification in notifications" :key="notification.id" class="notification-item">
                <div class="notification-content">
                  <h5>{{ notification.title }}</h5>
                  <p>{{ notification.content }}</p>
                  <span class="notification-time">{{ notification.time }}</span>
                </div>
                <el-button v-if="!notification.read" type="primary" size="small" @click="markAsRead(notification)">
                  标记已读
                </el-button>
              </div>
              
              <!-- 分页组件 -->
              <div class="pagination-container" v-if="notificationPagination.total > notificationPagination.size">
                <el-pagination
                  background
                  layout="prev, pager, next"
                  :total="notificationPagination.total"
                  :page-size="notificationPagination.size"
                  :current-page="notificationPagination.current"
                  @current-change="handleNotificationPageChange"
                />
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>
    
    <!-- 充值对话框 -->
    <el-dialog v-model="showRechargeDialog" title="账户充值" width="400px">
      <el-form :model="rechargeForm" label-width="80px">
        <el-form-item label="充值金额">
          <el-input-number v-model="rechargeForm.amount" :min="1" :max="1000" />
        </el-form-item>
        <el-form-item label="支付方式">
          <el-radio-group v-model="rechargeForm.payMethod">
            <el-radio label="alipay">支付宝</el-radio>
            <el-radio label="wechat">微信支付</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showRechargeDialog = false">取消</el-button>
        <el-button type="primary" @click="handleRecharge">确认充值</el-button>
      </template>
    </el-dialog>
    
    <!-- 密码修改对话框 -->
    <PasswordChangeDialog
      v-model="showPasswordDialog"
      @success="handlePasswordChangeSuccess"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useAuthStore } from '../../stores/auth.js'
import { ElMessage, ElLoading } from 'element-plus'
import AvatarUpload from '../../components/common/AvatarUpload.vue'
import PasswordChangeDialog from '../../components/common/PasswordChangeDialog.vue'
import { userApi, walletApi, notificationApi } from '../../request/userApi.js'

const authStore = useAuthStore()
const activeTab = ref('info')
const showRechargeDialog = ref(false)
const showPasswordDialog = ref(false)
const loading = ref(false)

const userInfo = reactive({
  realName: '',
  studentId: '',
  phone: '',
  email: '',
  avatar: '',
  alipayAccount: '',
  wechatAccount: ''
})

const wallet = reactive({
  balance: 0,
  transactions: [],
  total: 0,
  currentPage: 1,
  pageSize: 10
})

const notifications = ref([])
const notificationPagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

const rechargeForm = reactive({
  amount: 50,
  payMethod: 'alipay',
  remark: ''
})

// 获取用户详细资料
const getUserProfile = async () => {
  try {
    loading.value = true
    const response = await userApi.getUserProfile()
    if (response.code === 200) {
      Object.assign(userInfo, response.data)
    }
  } catch (error) {
    console.error('获取用户资料失败:', error)
    ElMessage.error('获取用户资料失败')
  } finally {
    loading.value = false
  }
}

// 获取钱包信息
const getWalletInfo = async () => {
  try {
    loading.value = true
    const [balanceRes, transactionsRes] = await Promise.all([
      walletApi.getBalance(),
      walletApi.getTransactions(wallet.currentPage, wallet.pageSize)
    ])
    
    if (balanceRes.code === 200) {
      wallet.balance = balanceRes.data.balance || 0
    } else {
      ElMessage.warning(balanceRes.msg || '获取余额信息失败')
    }
    
    if (transactionsRes.code === 200) {
      wallet.transactions = transactionsRes.data.records.map(record => ({
        time: record.createTime,
        type: getTransactionTypeName(record.transactionType),
        amount: record.amount,
        description: record.description || getDefaultDescription(record.transactionType, record.amount)
      }))
      wallet.total = transactionsRes.data.total || 0
    } else {
      ElMessage.warning(transactionsRes.msg || '获取交易记录失败')
    }
  } catch (error) {
    console.error('获取钱包信息失败:', error)
    ElMessage.error('获取钱包信息失败，请稍后再试')
  } finally {
    loading.value = false
  }
}

// 获取默认交易描述
const getDefaultDescription = (type, amount) => {
  const typeMap = {
    1: `充值 ¥${amount}`,
    2: `支付订单 ¥${amount}`,
    3: `收入 ¥${amount}`,
    4: `提现 ¥${amount}`,
    5: `退款 ¥${amount}`
  }
  return typeMap[type] || `交易 ¥${amount}`
}

// 获取通知列表
const getNotifications = async () => {
  try {
    loading.value = true
    const response = await notificationApi.getNotifications(
      notificationPagination.current, 
      notificationPagination.size
    )
    
    if (response.code === 200) {
      notifications.value = response.data.records.map(notice => ({
        id: notice.noticeId,
        title: notice.title,
        content: notice.content,
        time: notice.createTime,
        read: notice.readStatus === 1
      }))
      notificationPagination.total = response.data.total || 0
    } else {
      ElMessage.warning(response.msg || '获取通知列表失败')
    }
  } catch (error) {
    console.error('获取通知失败:', error)
    ElMessage.error('获取通知失败，请稍后再试')
  } finally {
    loading.value = false
  }
}

// 处理通知分页变化
const handleNotificationPageChange = (page) => {
  notificationPagination.current = page
  getNotifications()
}

// 更新个人信息
const updateProfile = async () => {
  try {
    const loadingInstance = ElLoading.service({ text: '保存中...' })
    
    const response = await userApi.updateUserProfile({
      realName: userInfo.realName,
      phone: userInfo.phone,
      email: userInfo.email,
      alipayAccount: userInfo.alipayAccount,
      wechatAccount: userInfo.wechatAccount,
      avatar: userInfo.avatar
    })
    
    loadingInstance.close()
    
    if (response.code === 200) {
      ElMessage.success('个人信息更新成功')
      // 更新认证状态中的用户信息
      authStore.updateUserInfo(userInfo)
    } else {
      ElMessage.error(response.msg || '更新失败')
    }
  } catch (error) {
    console.error('更新个人信息失败:', error)
    ElMessage.error('更新失败，请重试')
  }
}

// 充值处理
const handleRecharge = async () => {
  // 验证充值金额
  if (!rechargeForm.amount || rechargeForm.amount < 1) {
    ElMessage.warning('充值金额必须大于等于1元')
    return
  }
  
  try {
    const loadingInstance = ElLoading.service({ text: '充值中...' })
    
    const response = await walletApi.recharge({
      amount: rechargeForm.amount,
      payMethod: rechargeForm.payMethod,
      remark: rechargeForm.remark || `账户充值 ${rechargeForm.amount}元`
    })
    
    loadingInstance.close()
    
    if (response.code === 200) {
      showRechargeDialog.value = false
      ElMessage.success('充值成功')
      
      // 重置充值表单
      rechargeForm.amount = 50
      rechargeForm.payMethod = 'alipay'
      rechargeForm.remark = ''
      
      // 刷新钱包信息
      await getWalletInfo()
    } else {
      ElMessage.error(response.msg || '充值失败')
    }
  } catch (error) {
    console.error('充值失败:', error)
    ElMessage.error('充值失败，请稍后再试')
  }
}

// 标记通知为已读
const markAsRead = async (notification) => {
  try {
    const loadingInstance = ElLoading.service({ text: '处理中...' })
    const response = await notificationApi.markAsRead(notification.id)
    loadingInstance.close()
    
    if (response.code === 200) {
      notification.read = true
      ElMessage.success('已标记为已读')
      
      // 更新未读通知数量
      const unreadCountRes = await notificationApi.getUnreadCount()
      if (unreadCountRes.code === 200) {
        // 如果有全局通知状态管理，可以在这里更新
        console.log('未读通知数量:', unreadCountRes.data)
      }
    } else {
      ElMessage.error(response.msg || '操作失败')
    }
  } catch (error) {
    console.error('标记已读失败:', error)
    ElMessage.error('操作失败，请稍后再试')
  }
}

// 头像上传成功处理
const handleAvatarSuccess = async (file) => {
  try {
    const response = await userApi.uploadAvatar(file)
    if (response.code === 200) {
      userInfo.avatar = response.data
      ElMessage.success('头像上传成功')
      // 更新认证状态中的用户信息
      authStore.updateUserInfo({ avatar: response.data })
    } else {
      ElMessage.error(response.msg || '头像上传失败')
    }
  } catch (error) {
    console.error('头像上传失败:', error)
    ElMessage.error('头像上传失败，请重试')
  }
}

// 头像上传失败处理
const handleAvatarError = (error) => {
  console.error('头像上传失败:', error)
  ElMessage.error('头像上传失败，请重试')
}

// 头像删除成功处理
const handleAvatarDelete = async () => {
  try {
    const response = await userApi.deleteAvatar()
    if (response.code === 200) {
      userInfo.avatar = ''
      ElMessage.success('头像删除成功')
    }
  } catch (error) {
    console.error('头像删除失败:', error)
    ElMessage.error('头像删除失败')
  }
}

// 头像预览处理
const handleAvatarPreview = (avatarPath) => {
  console.log('预览头像:', avatarPath)
}

// 获取交易类型名称
const getTransactionTypeName = (type) => {
  const typeMap = {
    1: 'recharge', // 充值
    2: 'payment',  // 支付
    3: 'income',   // 收入
    4: 'withdraw', // 提现
    5: 'refund'    // 退款
  }
  return typeMap[type] || 'unknown'
}

// 获取交易类型显示文本
const getTransactionTypeText = (type) => {
  const typeMap = {
    'recharge': '充值',
    'payment': '支付',
    'income': '收入',
    'withdraw': '提现',
    'refund': '退款',
    'unknown': '未知'
  }
  return typeMap[type] || '未知'
}

// 密码修改成功处理
const handlePasswordChangeSuccess = () => {
  ElMessage.success('密码修改成功，请重新登录')
  // 可以选择自动登出用户
  setTimeout(() => {
    authStore.logout()
    window.location.href = '/login'
  }, 2000)
}

// 获取交易类型对应的标签类型
const getTagType = (type) => {
  const typeTagMap = {
    'recharge': 'success',
    'payment': 'warning',
    'income': 'success',
    'withdraw': 'info',
    'refund': 'primary',
    'unknown': 'info'
  }
  return typeTagMap[type] || 'info'
}

// 获取交易金额的CSS类名
const getAmountClass = (type) => {
  if (['recharge', 'income', 'refund'].includes(type)) {
    return 'income'
  } else if (['payment', 'withdraw'].includes(type)) {
    return 'expense'
  }
  return ''
}

// 获取交易金额前缀（+/-）
const getAmountPrefix = (type) => {
  if (['recharge', 'income', 'refund'].includes(type)) {
    return '+'
  } else if (['payment', 'withdraw'].includes(type)) {
    return '-'
  }
  return ''
}

// 处理交易记录分页变化
const handleTransactionPageChange = async (page) => {
  try {
    loading.value = true
    const response = await walletApi.getTransactions(page, 10)
    
    if (response.code === 200) {
      wallet.transactions = response.data.records.map(record => ({
        time: record.createTime,
        type: getTransactionTypeName(record.transactionType),
        amount: record.amount,
        description: record.description || getDefaultDescription(record.transactionType, record.amount)
      }))
      wallet.total = response.data.total
    } else {
      ElMessage.warning(response.msg || '获取交易记录失败')
    }
  } catch (error) {
    console.error('获取交易记录失败:', error)
    ElMessage.error('获取交易记录失败，请稍后再试')
  } finally {
    loading.value = false
  }
}

onMounted(async () => {
  // 初始化数据
  await Promise.all([
    getUserProfile(),
    getWalletInfo(),
    getNotifications()
  ])
})
</script>

<style scoped>
.profile-container {
  width: 100%;
  height: 100%;
  padding: 8px;
  overflow-y: auto;
  display: flex;
  justify-content: center;
}

.profile-container > .el-card {
  width: 100%;
  max-width: 800px;
}

.profile-form {
  max-width: 500px;
}

.wallet-section {
  max-width: 600px;
}

.balance-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 30px;
  border-radius: 12px;
  text-align: center;
  margin-bottom: 30px;
}

.balance-card h4 {
  margin: 0 0 10px 0;
  opacity: 0.9;
}

.balance-amount {
  font-size: 36px;
  font-weight: bold;
  margin: 15px 0;
}

.transaction-history h4 {
  margin-bottom: 20px;
}

.income {
  color: #67c23a;
}

.expense {
  color: #f56c6c;
}

.notifications-section {
  max-width: 600px;
}

.notification-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 20px;
  border: 1px solid #e6e6e6;
  border-radius: 8px;
  margin-bottom: 15px;
}

.notification-content {
  flex: 1;
}

.notification-content h5 {
  margin: 0 0 8px 0;
  color: #303133;
}

.notification-content p {
  margin: 0 0 8px 0;
  color: #606266;
}

.notification-time {
  font-size: 12px;
  color: #909399;
}

.empty-notifications {
  text-align: center;
  padding: 40px 0;
}

.avatar-section {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  flex-wrap: wrap;
}

.avatar-tips {
  color: #909399;
  font-size: 12px;
  line-height: 1.5;
  max-width: 200px;
  flex: 1;
}

.avatar-tips p {
  margin: 4px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .avatar-section {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }
  
  .avatar-tips {
    max-width: none;
    text-align: center;
  }
}
</style>