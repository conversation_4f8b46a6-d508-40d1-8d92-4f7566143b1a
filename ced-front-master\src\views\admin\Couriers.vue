<template>
  <div class="couriers-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <h3>代取员管理</h3>
          <div class="header-actions">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索代取员"
              style="width: 200px; margin-right: 10px;"
              clearable
            />
            <el-button type="primary" @click="searchCouriers">搜索</el-button>
          </div>
        </div>
      </template>
      
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <el-tab-pane label="待审核" name="pending" />
        <el-tab-pane label="已通过" name="approved" />
        <el-tab-pane label="已拒绝" name="rejected" />
        <el-tab-pane label="全部" name="all" />
      </el-tabs>
      
      <div class="filter-bar">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-select v-model="filters.rating" placeholder="评分范围" clearable>
              <el-option label="5星" value="5" />
              <el-option label="4-5星" value="4-5" />
              <el-option label="3-4星" value="3-4" />
              <el-option label="3星以下" value="0-3" />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-select v-model="filters.orderCount" placeholder="接单数量" clearable>
              <el-option label="100单以上" value="100+" />
              <el-option label="50-100单" value="50-100" />
              <el-option label="10-50单" value="10-50" />
              <el-option label="10单以下" value="0-10" />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-date-picker
              v-model="filters.registerDate"
              type="daterange"
              range-separator="至"
              start-placeholder="注册开始日期"
              end-placeholder="注册结束日期"
              size="default"
            />
          </el-col>
          <el-col :span="6">
            <el-button @click="applyFilters">筛选</el-button>
            <el-button @click="clearFilters">清空</el-button>
          </el-col>
        </el-row>
      </div>
      
      <el-table :data="filteredCouriers" style="width: 100%" v-loading="loading">
        <el-table-column prop="id" label="代取员ID" width="120" />
        <el-table-column prop="name" label="姓名" width="100" />
        <el-table-column prop="studentId" label="学号" width="120" />
        <el-table-column prop="phone" label="手机号" width="130" />
        <el-table-column prop="idCard" label="身份证号" width="180">
          <template #default="scope">
            {{ hideIdCard(scope.row.idCard) }}
          </template>
        </el-table-column>
        <el-table-column prop="alipayAccount" label="支付宝账号" width="150" />
        <el-table-column prop="orderCount" label="接单数" width="80" />
        <el-table-column prop="rating" label="评分" width="120">
          <template #default="scope">
            <el-rate v-model="scope.row.rating" disabled show-score text-color="#ff9900" />
          </template>
        </el-table-column>
        <el-table-column prop="totalEarnings" label="累计收入" width="100">
          <template #default="scope">
            ¥{{ scope.row.totalEarnings }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="registerTime" label="申请时间" width="180">
          <template #default="scope">
            {{ formatTime(scope.row.registerTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="scope">
            <el-button type="primary" size="small" @click="viewCourierDetail(scope.row)">
              详情
            </el-button>
            <template v-if="scope.row.status === 'pending'">
              <el-button type="success" size="small" @click="approveCourier(scope.row)">
                通过
              </el-button>
              <el-button type="danger" size="small" @click="rejectCourier(scope.row)">
                拒绝
              </el-button>
            </template>
            <template v-else-if="scope.row.status === 'approved'">
              <el-button type="warning" size="small" @click="suspendCourier(scope.row)">
                暂停
              </el-button>
            </template>
            <template v-else-if="scope.row.status === 'suspended'">
              <el-button type="success" size="small" @click="activateCourier(scope.row)">
                激活
              </el-button>
            </template>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="totalCouriers"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    
    <!-- 代取员详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="代取员详情" width="800px">
      <div v-if="selectedCourier" class="courier-detail">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-descriptions title="基本信息" :column="1" border>
              <el-descriptions-item label="姓名">{{ selectedCourier.name }}</el-descriptions-item>
              <el-descriptions-item label="学号">{{ selectedCourier.studentId }}</el-descriptions-item>
              <el-descriptions-item label="身份证号">{{ selectedCourier.idCard }}</el-descriptions-item>
              <el-descriptions-item label="手机号">{{ selectedCourier.phone }}</el-descriptions-item>
              <el-descriptions-item label="支付宝账号">{{ selectedCourier.alipayAccount }}</el-descriptions-item>
              <el-descriptions-item label="申请时间">{{ formatTime(selectedCourier.registerTime) }}</el-descriptions-item>
              <el-descriptions-item label="审核状态">
                <el-tag :type="getStatusType(selectedCourier.status)">
                  {{ getStatusText(selectedCourier.status) }}
                </el-tag>
              </el-descriptions-item>
            </el-descriptions>
          </el-col>
          
          <el-col :span="12">
            <el-descriptions title="服务统计" :column="1" border>
              <el-descriptions-item label="接单数量">{{ selectedCourier.orderCount }}</el-descriptions-item>
              <el-descriptions-item label="完成订单">{{ selectedCourier.completedOrders }}</el-descriptions-item>
              <el-descriptions-item label="取消订单">{{ selectedCourier.cancelledOrders }}</el-descriptions-item>
              <el-descriptions-item label="服务评分">
                <el-rate v-model="selectedCourier.rating" disabled show-score text-color="#ff9900" />
              </el-descriptions-item>
              <el-descriptions-item label="累计收入">¥{{ selectedCourier.totalEarnings }}</el-descriptions-item>
              <el-descriptions-item label="本月收入">¥{{ selectedCourier.monthlyEarnings }}</el-descriptions-item>
              <el-descriptions-item label="投诉次数">{{ selectedCourier.complaints }}</el-descriptions-item>
            </el-descriptions>
          </el-col>
        </el-row>
        
        <div class="courier-documents" style="margin-top: 20px;">
          <h4>认证材料</h4>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="document-item">
                <h5>学生证照片</h5>
                <el-image
                  style="width: 200px; height: 150px"
                  :src="selectedCourier.studentCardImage"
                  :preview-src-list="[selectedCourier.studentCardImage]"
                  fit="cover"
                />
              </div>
            </el-col>
            <el-col :span="12">
              <div class="document-item">
                <h5>身份证照片</h5>
                <el-image
                  style="width: 200px; height: 150px"
                  :src="selectedCourier.idCardImage"
                  :preview-src-list="[selectedCourier.idCardImage]"
                  fit="cover"
                />
              </div>
            </el-col>
          </el-row>
        </div>
        
        <div class="recent-orders" style="margin-top: 20px;">
          <h4>最近订单</h4>
          <el-table :data="selectedCourier.recentOrders" style="width: 100%">
            <el-table-column prop="id" label="订单号" width="120" />
            <el-table-column prop="userName" label="用户" width="100" />
            <el-table-column prop="expressPoint" label="快递点" />
            <el-table-column prop="fee" label="费用">
              <template #default="scope">
                ¥{{ scope.row.fee }}
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态">
              <template #default="scope">
                <el-tag :type="getOrderStatusType(scope.row.status)">
                  {{ getOrderStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="时间">
              <template #default="scope">
                {{ formatTime(scope.row.createTime) }}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-dialog>
    
    <!-- 审核对话框 -->
    <el-dialog v-model="reviewDialogVisible" :title="reviewAction === 'approve' ? '审核通过' : '审核拒绝'" width="500px">
      <el-form :model="reviewForm" label-width="100px">
        <el-form-item label="审核意见">
          <el-input
            v-model="reviewForm.comment"
            type="textarea"
            :rows="4"
            :placeholder="reviewAction === 'approve' ? '审核通过原因（可选）' : '请说明拒绝原因'"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="reviewDialogVisible = false">取消</el-button>
        <el-button 
          :type="reviewAction === 'approve' ? 'success' : 'danger'" 
          @click="confirmReview"
        >
          确认{{ reviewAction === 'approve' ? '通过' : '拒绝' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useOrderStore } from '../../stores/order.js'
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'
import { adminApi } from '../../request/adminApi.js'

const orderStore = useOrderStore()

const loading = ref(false)
const searchKeyword = ref('')
const activeTab = ref('pending')
const detailDialogVisible = ref(false)
const reviewDialogVisible = ref(false)
const selectedCourier = ref(null)
const reviewAction = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const totalCount = ref(0)

const filters = reactive({
  rating: '',
  orderCount: '',
  registerDate: []
})

const reviewForm = reactive({
  comment: ''
})

// 代取员数据
const couriers = ref([])

// 可用代取员列表（用于分配订单）
const availableCouriers = ref([])

const filteredCouriers = computed(() => {
  return couriers.value
})

const totalCouriers = computed(() => totalCount.value)

const formatTime = (time) => {
  if (!time) return ''
  return new Date(time).toLocaleString()
}

const hideIdCard = (idCard) => {
  if (!idCard) return ''
  return idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2')
}

const getStatusType = (status) => {
  const statusMap = {
    'pending': 'warning',
    'approved': 'success',
    'rejected': 'danger',
    'suspended': 'info'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    'pending': '待审核',
    'approved': '已通过',
    'rejected': '已拒绝',
    'suspended': '已暂停'
  }
  return statusMap[status] || '未知状态'
}

const getOrderStatusType = (status) => {
  const statusMap = {
    [orderStore.ORDER_STATUS.PENDING]: 'warning',
    [orderStore.ORDER_STATUS.ACCEPTED]: 'primary',
    [orderStore.ORDER_STATUS.COMPLETED]: 'success',
    [orderStore.ORDER_STATUS.CANCELLED]: 'danger'
  }
  return statusMap[status] || 'info'
}

const getOrderStatusText = (status) => {
  const statusMap = {
    [orderStore.ORDER_STATUS.PENDING]: '待接单',
    [orderStore.ORDER_STATUS.ACCEPTED]: '已接单',
    [orderStore.ORDER_STATUS.COMPLETED]: '已完成',
    [orderStore.ORDER_STATUS.CANCELLED]: '已取消'
  }
  return statusMap[status] || '未知状态'
}

// 根据标签页加载不同状态的代取员
const handleTabChange = (tabName) => {
  activeTab.value = tabName
  currentPage.value = 1
  fetchCouriers()
}

// 搜索代取员
const searchCouriers = () => {
  currentPage.value = 1
  fetchCouriers()
}

// 应用筛选条件
const applyFilters = () => {
  currentPage.value = 1
  fetchCouriers()
}

// 清空筛选条件
const clearFilters = () => {
  filters.rating = ''
  filters.orderCount = ''
  filters.registerDate = []
  ElMessage.info('筛选条件已清空')
  currentPage.value = 1
  fetchCouriers()
}

// 查看代取员详情
const viewCourierDetail = async (courier) => {
  const loadingInstance = ElLoading.service({ text: '加载代取员详情...' })
  
  try {
    const response = await adminApi.getCourierDetail(courier.id)
    
    if (response.code === 200) {
      selectedCourier.value = response.data
      detailDialogVisible.value = true
    } else {
      ElMessage.error(response.msg || '获取代取员详情失败')
    }
  } catch (error) {
    console.error('获取代取员详情失败:', error)
    ElMessage.error('获取代取员详情失败，请重试')
  } finally {
    loadingInstance.close()
  }
}

// 审核通过
const approveCourier = (courier) => {
  selectedCourier.value = courier
  reviewAction.value = 'approve'
  reviewForm.comment = ''
  reviewDialogVisible.value = true
}

// 审核拒绝
const rejectCourier = (courier) => {
  selectedCourier.value = courier
  reviewAction.value = 'reject'
  reviewForm.comment = ''
  reviewDialogVisible.value = true
}

// 确认审核
const confirmReview = async () => {
  if (reviewAction.value === 'reject' && !reviewForm.comment.trim()) {
    ElMessage.warning('请填写拒绝原因')
    return
  }
  
  const loadingInstance = ElLoading.service({ text: '提交审核结果...' })
  
  try {
    const status = reviewAction.value === 'approve' ? 'approved' : 'rejected'
    const response = await adminApi.auditCourier(
      selectedCourier.value.id, 
      status, 
      reviewForm.comment
    )
    
    if (response.code === 200) {
      ElMessage.success(reviewAction.value === 'approve' ? '代取员审核通过' : '代取员审核拒绝')
      fetchCouriers() // 刷新列表
    } else {
      ElMessage.error(response.msg || '审核操作失败')
    }
  } catch (error) {
    console.error('审核操作失败:', error)
    ElMessage.error('审核操作失败，请重试')
  } finally {
    loadingInstance.close()
    reviewDialogVisible.value = false
  }
}

// 暂停代取员
const suspendCourier = async (courier) => {
  try {
    await ElMessageBox.confirm(`确定要暂停代取员 ${courier.name} 吗？`, '暂停代取员', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const loadingInstance = ElLoading.service({ text: '正在暂停代取员...' })
    
    try {
      const response = await adminApi.updateCourierStatus(courier.id, 'suspended')
      
      if (response.code === 200) {
        ElMessage.success('代取员已暂停')
        fetchCouriers() // 刷新列表
      } else {
        ElMessage.error(response.msg || '暂停代取员失败')
      }
    } catch (error) {
      console.error('暂停代取员失败:', error)
      ElMessage.error('暂停代取员失败，请重试')
    } finally {
      loadingInstance.close()
    }
  } catch {
    // 用户取消
  }
}

// 激活代取员
const activateCourier = async (courier) => {
  try {
    await ElMessageBox.confirm(`确定要激活代取员 ${courier.name} 吗？`, '激活代取员', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'success'
    })
    
    const loadingInstance = ElLoading.service({ text: '正在激活代取员...' })
    
    try {
      const response = await adminApi.updateCourierStatus(courier.id, 'approved')
      
      if (response.code === 200) {
        ElMessage.success('代取员已激活')
        fetchCouriers() // 刷新列表
      } else {
        ElMessage.error(response.msg || '激活代取员失败')
      }
    } catch (error) {
      console.error('激活代取员失败:', error)
      ElMessage.error('激活代取员失败，请重试')
    } finally {
      loadingInstance.close()
    }
  } catch {
    // 用户取消
  }
}

const handleSizeChange = (size) => {
  pageSize.value = size
  fetchCouriers()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  fetchCouriers()
}

// 获取代取员列表
const fetchCouriers = async () => {
  loading.value = true
  
  try {
    let response
    
    // 根据当前标签页获取不同状态的代取员
    if (activeTab.value === 'pending') {
      response = await adminApi.getPendingCouriers()
    } else if (activeTab.value === 'approved') {
      response = await adminApi.getApprovedCouriers()
    } else if (activeTab.value === 'rejected') {
      response = await adminApi.getRejectedCouriers()
    } else {
      // 获取所有代取员，带筛选条件
      const params = {
        current: currentPage.value,
        size: pageSize.value,
        keyword: searchKeyword.value || undefined
      }
      
      // 添加评分筛选
      if (filters.rating) {
        switch (filters.rating) {
          case '5':
            params.minRating = 4.5
            break
          case '4-5':
            params.minRating = 4
            params.maxRating = 4.5
            break
          case '3-4':
            params.minRating = 3
            params.maxRating = 4
            break
          case '0-3':
            params.maxRating = 3
            break
        }
      }
      
      // 添加订单数量筛选
      if (filters.orderCount) {
        switch (filters.orderCount) {
          case '100+':
            params.minOrderCount = 100
            break
          case '50-100':
            params.minOrderCount = 50
            params.maxOrderCount = 100
            break
          case '10-50':
            params.minOrderCount = 10
            params.maxOrderCount = 50
            break
          case '0-10':
            params.maxOrderCount = 10
            break
        }
      }
      
      // 添加日期筛选
      if (filters.registerDate && filters.registerDate.length === 2) {
        params.startDate = filters.registerDate[0].toISOString().split('T')[0]
        params.endDate = filters.registerDate[1].toISOString().split('T')[0]
      }
      
      response = await adminApi.getCouriers(params)
    }
    
    if (response.code === 200) {
      couriers.value = response.data.records
      totalCount.value = response.data.total
    } else {
      ElMessage.error(response.msg || '获取代取员列表失败')
    }
  } catch (error) {
    console.error('获取代取员列表失败:', error)
    ElMessage.error('获取代取员列表失败，请重试')
  } finally {
    loading.value = false
  }
}

// 获取可用代取员列表（用于分配订单）
const fetchAvailableCouriers = async () => {
  try {
    const response = await adminApi.getApprovedCouriers()
    
    if (response.code === 200) {
      availableCouriers.value = response.data.records.map(courier => ({
        id: courier.id,
        name: courier.name,
        rating: courier.rating
      }))
    }
  } catch (error) {
    console.error('获取可用代取员列表失败:', error)
  }
}

onMounted(() => {
  // 初始化数据
  fetchCouriers()
  fetchAvailableCouriers()
})

// 监听标签页变化
watch(activeTab, () => {
  fetchCouriers()
})
</script>

<style scoped>
.couriers-container {
  width: 100%;
  height: 100%;
  padding: 8px;
  overflow-y: auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: #303133;
}

.header-actions {
  display: flex;
  align-items: center;
}

.filter-bar {
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.courier-detail {
  max-height: 600px;
  overflow-y: auto;
}

.courier-documents h4,
.recent-orders h4 {
  margin-bottom: 15px;
  color: #303133;
}

.document-item {
  text-align: center;
}

.document-item h5 {
  margin-bottom: 10px;
  color: #606266;
}
</style>