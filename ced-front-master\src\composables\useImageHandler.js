import { ref, onUnmounted } from 'vue';
import { ElMessage } from 'element-plus';
import { uploadFileApi, downloadFile, deleteFileApi } from '../request/fileApi';

/**
 * 图片处理组合式函数
 * 提供图片上传、下载、显示、删除等功能
 */
export function useImageHandler() {
  // 响应式状态
  const loading = ref(false);
  const displayUrl = ref('');
  const selectedFile = ref(null);

  // 图片缓存Map，避免重复下载
  const imageCache = new Map();

  /**
   * 下载并显示图片
   * @param {string} imagePath - 图片路径
   * @param {string} imageType - 图片类型，默认为 'image/jpeg'
   * @returns {Promise<string>} 返回生成的显示URL
   */
  const loadImage = async (imagePath, imageType = 'image/jpeg') => {
    if (!imagePath || imagePath.trim() === '') {
      console.log('图片路径为空，跳过加载');
      clearDisplayUrl();
      return '';
    }

    try {
      loading.value = true;
      console.log('开始下载图片，路径:', imagePath);

      // 清理旧的URL对象
      clearDisplayUrl();

      // 检查缓存中是否已有该图片
      if (imageCache.has(imagePath)) {
        console.log('从缓存中获取图片:', imagePath);
        const cachedUrl = imageCache.get(imagePath);
        displayUrl.value = cachedUrl;
        loading.value = false;
        return cachedUrl;
      }

      // 下载图片文件
      const response = await downloadFile(imagePath);

      // 检查响应是否为Blob类型
      let blob;
      if (response instanceof Blob) {
        blob = response;
      } else {
        // 如果响应不是Blob，尝试转换
        blob = new Blob([response], { type: imageType });
      }

      // 验证Blob是否有效
      if (blob.size === 0) {
        throw new Error('图片文件为空');
      }

      // 创建可显示的URL
      const imageUrl = URL.createObjectURL(blob);

      // 预加载图片以确保能正常显示
      await new Promise((resolve, reject) => {
        const img = new Image();
        img.onload = () => {
          console.log('图片预加载成功');
          resolve();
        };
        img.onerror = () => {
          URL.revokeObjectURL(imageUrl);
          reject(new Error('图片格式无效或损坏'));
        };
        img.src = imageUrl;
      });

      // 设置图片显示URL
      displayUrl.value = imageUrl;

      // 将图片URL添加到缓存
      imageCache.set(imagePath, imageUrl);

      console.log('图片下载并显示成功');
      return imageUrl;
    } catch (error) {
      console.error('加载图片失败:', error);

      // 根据错误类型提供不同的提示
      let errorMessage = '图片加载失败';
      if (error.message.includes('网络')) {
        errorMessage = '网络连接失败，请检查网络';
      } else if (error.message.includes('格式') || error.message.includes('损坏')) {
        errorMessage = '图片格式无效或文件损坏';
      } else if (error.message.includes('空')) {
        errorMessage = '图片文件不存在或为空';
      }

      ElMessage.warning(errorMessage);
      clearDisplayUrl();
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 上传图片
   * @param {File} file - 要上传的文件
   * @param {string} uploadPath - 上传路径
   * @param {string} oldImagePath - 原有图片路径（用于删除）
   * @returns {Promise<string>} 返回上传后的文件路径
   */
  const uploadImage = async (file, uploadPath, oldImagePath = null) => {
    if (!file) {
      ElMessage.warning('请先选择图片文件');
      throw new Error('No file selected');
    }

    try {
      loading.value = true;
      console.log('开始上传图片:', {
        fileName: file.name,
        fileSize: file.size,
        fileType: file.type,
        uploadPath: uploadPath
      });

      // 上传图片文件
      const res = await uploadFileApi({
        file: file,
        path: uploadPath
      });

      console.log('上传API响应:', res);

      if (res && res.code === 200) {
        console.log('图片上传成功');

        // 删除原有图片文件（如果存在）
        if (oldImagePath && oldImagePath.trim() !== '') {
          await deleteOldImage(oldImagePath);
        }

        return uploadPath;
      } else {
        const errorMsg = res?.msg || '上传失败，服务器响应异常';
        console.error('上传失败，服务器响应:', res);
        ElMessage.error(errorMsg);
        throw new Error(errorMsg);
      }
    } catch (error) {
      console.error('上传图片失败:', error);

      // 提供更详细的错误信息
      let errorMessage = '网络异常，请稍后再试';
      if (error.response) {
        // 服务器响应了错误状态码
        errorMessage = `服务器错误: ${error.response.status} - ${error.response.data?.msg || error.response.statusText}`;
      } else if (error.request) {
        // 请求已发出但没有收到响应
        errorMessage = '网络连接失败，请检查网络连接';
      } else if (error.message) {
        // 其他错误
        errorMessage = error.message;
      }

      ElMessage.error(errorMessage);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 删除图片文件
   * @param {string} imagePath - 要删除的图片路径
   * @returns {Promise<boolean>} 删除是否成功
   */
  const deleteImage = async (imagePath) => {
    if (!imagePath || imagePath.trim() === '') {
      console.log('图片路径为空，跳过删除');
      return true;
    }

    try {
      console.log('删除图片:', imagePath);
      await deleteFileApi({ path: imagePath });
      console.log('图片删除成功');
      return true;
    } catch (error) {
      console.warn('删除图片失败:', error);
      return false;
    }
  };

  /**
   * 删除原有图片（内部使用，不抛出错误）
   * @param {string} imagePath - 要删除的图片路径
   */
  const deleteOldImage = async (imagePath) => {
    try {
      await deleteImage(imagePath);
    } catch (error) {
      // 删除失败不影响主流程，只记录警告
      console.warn('删除原有图片失败:', error);
    }
  };

  /**
   * 验证图片文件
   * @param {File} file - 要验证的文件
   * @param {Object} options - 验证选项
   * @param {Array} options.allowedTypes - 允许的文件类型
   * @param {number} options.maxSize - 最大文件大小（MB）
   * @returns {boolean} 验证是否通过
   */
  const validateImage = (file, options = {}) => {
    const {
      allowedTypes = ['image/jpeg', 'image/png'],
      maxSize = 2
    } = options;

    if (!allowedTypes.includes(file.type)) {
      ElMessage.error(`图片格式不支持，请选择 ${allowedTypes.map(type => type.split('/')[1].toUpperCase()).join('、')} 格式的图片`);
      return false;
    }

    if (file.size / 1024 / 1024 > maxSize) {
      ElMessage.error(`图片大小不能超过 ${maxSize}MB`);
      return false;
    }

    return true;
  };

  /**
   * 处理文件选择变化
   * @param {Object} fileInfo - 文件信息对象
   * @param {Object} options - 验证选项
   * @returns {boolean} 处理是否成功
   */
  const handleFileChange = (fileInfo, options = {}) => {
    const file = fileInfo.raw || fileInfo;

    if (!validateImage(file, options)) {
      return false;
    }

    selectedFile.value = file;

    // 创建预览
    const reader = new FileReader();
    reader.onload = (e) => {
      // 清理旧的URL
      clearDisplayUrl();
      // 设置预览URL
      displayUrl.value = e.target.result;
    };
    reader.readAsDataURL(file);

    return true;
  };

  /**
   * 生成唯一文件路径
   * @param {string} originalName - 原始文件名
   * @param {string} directory - 目录名，默认为 'images'
   * @returns {string} 生成的文件路径
   */
  const generateFilePath = (originalName, directory = 'images') => {
    const uuid = crypto.randomUUID ? crypto.randomUUID() : Date.now().toString(36) + Math.random().toString(36).substring(2);
    const fileName = originalName.split('.')[0];
    const fileExtension = originalName.split('.').pop();
    return `/${directory}/${fileName}_${uuid}.${fileExtension}`;
  };

  /**
   * 清理显示URL
   */
  const clearDisplayUrl = () => {
    if (displayUrl.value && displayUrl.value.startsWith('blob:')) {
      // 检查是否在缓存中，如果在缓存中则不释放URL
      const isInCache = Array.from(imageCache.values()).includes(displayUrl.value);
      if (!isInCache) {
        URL.revokeObjectURL(displayUrl.value);
      }
    }
    displayUrl.value = '';
  };

  /**
   * 清理图片缓存
   */
  const clearImageCache = () => {
    imageCache.forEach((url) => {
      if (url.startsWith('blob:')) {
        URL.revokeObjectURL(url);
      }
    });
    imageCache.clear();
  };

  /**
   * 重置所有状态
   */
  const reset = () => {
    clearDisplayUrl();
    selectedFile.value = null;
    loading.value = false;
  };

  // 组件卸载时清理资源
  onUnmounted(() => {
    clearDisplayUrl();
    clearImageCache();
  });

  return {
    // 状态
    loading,
    displayUrl,
    selectedFile,

    // 方法
    loadImage,
    uploadImage,
    deleteImage,
    validateImage,
    handleFileChange,
    generateFilePath,
    clearDisplayUrl,
    clearImageCache,
    reset
  };
}
