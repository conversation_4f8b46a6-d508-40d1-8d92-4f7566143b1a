<template>
  <div class="courier-orders-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <h3>订单大厅</h3>
          <div class="header-actions">
            <el-button @click="refreshOrders" :loading="refreshing">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            <el-switch
              v-model="autoRefresh"
              active-text="自动刷新"
              @change="toggleAutoRefresh"
            />
          </div>
        </div>
      </template>
      
      <div class="filter-bar">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-select v-model="filters.campus" placeholder="选择校区" clearable>
              <el-option label="本部校区" value="main" />
              <el-option label="南校区" value="south" />
              <el-option label="北校区" value="north" />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-select v-model="filters.expressPoint" placeholder="选择快递点" clearable>
              <el-option label="菜鸟驿站" value="cainiao" />
              <el-option label="京东站点" value="jd" />
              <el-option label="顺丰站点" value="sf" />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-select v-model="filters.feeRange" placeholder="费用范围" clearable>
              <el-option label="2-5元" value="2-5" />
              <el-option label="5-10元" value="5-10" />
              <el-option label="10元以上" value="10+" />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-button type="primary" @click="applyFilters">筛选</el-button>
            <el-button @click="clearFilters">清空</el-button>
          </el-col>
        </el-row>
      </div>
      
      <div class="orders-list">
        <div v-if="filteredOrders.length === 0" class="empty-state">
          <el-empty description="暂无可接订单" :image-size="100">
            <el-button type="primary" @click="refreshOrders">刷新订单</el-button>
          </el-empty>
        </div>
        
        <div v-else>
          <el-card v-for="order in filteredOrders" :key="order.id" class="order-card" shadow="hover">
            <div class="order-header">
              <div class="order-info">
                <h4>{{ order.expressPointName }}</h4>
                <p class="order-time">{{ formatTime(order.createTime) }}</p>
              </div>
              <div class="order-fee">
                <span class="fee-amount">¥{{ order.fee }}</span>
                <span class="fee-label">代取费</span>
              </div>
            </div>
            
            <el-divider />
            
            <div class="order-content">
              <el-row :gutter="20">
                <el-col :span="12">
                  <div class="info-item">
                    <el-icon class="info-icon"><Location /></el-icon>
                    <span class="info-text">{{ order.deliveryAddress }}</span>
                  </div>
                  <div class="info-item">
                    <el-icon class="info-icon"><Clock /></el-icon>
                    <span class="info-text">{{ formatTime(order.expectedTime) }}</span>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="info-item">
                    <el-icon class="info-icon"><Box /></el-icon>
                    <span class="info-text">取件码：{{ order.pickupCode }}</span>
                  </div>
                  <div class="info-item" v-if="order.description">
                    <el-icon class="info-icon"><Document /></el-icon>
                    <span class="info-text">{{ order.description }}</span>
                  </div>
                </el-col>
              </el-row>
              
              <div v-if="order.specialRequirements" class="special-requirements">
                <el-tag type="warning" size="small">特殊要求</el-tag>
                <span>{{ order.specialRequirements }}</span>
              </div>
            </div>
            
            <el-divider />
            
            <div class="order-actions">
              <div class="distance-info">
                <el-icon><MapLocation /></el-icon>
                <span>距离约 {{ getRandomDistance() }}km</span>
              </div>
              <el-button 
                type="primary" 
                @click="handleAcceptOrder(order)"
                :loading="acceptingOrders.includes(order.id)"
              >
                接单
              </el-button>
            </div>
          </el-card>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, reactive, onMounted, onUnmounted } from 'vue'
import { useOrderStore } from '../../stores/order.js'
import { useAuthStore } from '../../stores/auth.js'
import { ElMessage } from 'element-plus'
import { Refresh, Location, Clock, Box, Document, MapLocation } from '@element-plus/icons-vue'

const orderStore = useOrderStore()
const authStore = useAuthStore()

const refreshing = ref(false)
const autoRefresh = ref(false)
const acceptingOrders = ref([])
let refreshTimer = null

const filters = reactive({
  campus: '',
  expressPoint: '',
  feeRange: ''
})

const pendingOrders = computed(() => {
  return orderStore.getPendingOrders().sort((a, b) => 
    new Date(b.createTime) - new Date(a.createTime)
  )
})

const filteredOrders = computed(() => {
  let orders = pendingOrders.value
  
  if (filters.campus) {
    orders = orders.filter(order => order.campus === filters.campus)
  }
  
  if (filters.expressPoint) {
    orders = orders.filter(order => order.expressPoint.includes(filters.expressPoint))
  }
  
  if (filters.feeRange) {
    orders = orders.filter(order => {
      const fee = order.fee
      switch (filters.feeRange) {
        case '2-5':
          return fee >= 2 && fee <= 5
        case '5-10':
          return fee > 5 && fee <= 10
        case '10+':
          return fee > 10
        default:
          return true
      }
    })
  }
  
  return orders
})

const formatTime = (time) => {
  return new Date(time).toLocaleString()
}

const getRandomDistance = () => {
  return (Math.random() * 2 + 0.5).toFixed(1)
}

const refreshOrders = async () => {
  refreshing.value = true
  // 模拟刷新延迟
  await new Promise(resolve => setTimeout(resolve, 1000))
  refreshing.value = false
  ElMessage.success('订单已刷新')
}

const toggleAutoRefresh = (value) => {
  if (value) {
    refreshTimer = setInterval(() => {
      refreshOrders()
    }, 30000) // 30秒自动刷新
    ElMessage.info('已开启自动刷新')
  } else {
    if (refreshTimer) {
      clearInterval(refreshTimer)
      refreshTimer = null
    }
    ElMessage.info('已关闭自动刷新')
  }
}

const applyFilters = () => {
  ElMessage.success('筛选条件已应用')
}

const clearFilters = () => {
  filters.campus = ''
  filters.expressPoint = ''
  filters.feeRange = ''
  ElMessage.info('筛选条件已清空')
}

const handleAcceptOrder = async (order) => {
  acceptingOrders.value.push(order.id)
  
  try {
    // 模拟接单过程
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    // 更新订单状态并分配代取员
    orderStore.updateOrderStatus(order.id, orderStore.ORDER_STATUS.ACCEPTED)
    
    // 在实际应用中，这里应该通过API更新订单的代取员信息
    const orderIndex = orderStore.orders.findIndex(o => o.id === order.id)
    if (orderIndex !== -1) {
      orderStore.orders[orderIndex].courierId = authStore.user.id
      orderStore.orders[orderIndex].courierName = authStore.user.name
    }
    
    ElMessage.success('接单成功！请及时前往取件')
  } catch (error) {
    ElMessage.error('接单失败，请重试')
  } finally {
    acceptingOrders.value = acceptingOrders.value.filter(id => id !== order.id)
  }
}

onMounted(() => {
  // 初始化时添加一些示例订单
  if (orderStore.orders.length === 0) {
    const sampleOrders = [
      {
        expressPoint: 'main_cainiao',
        expressPointName: '本部菜鸟驿站',
        pickupCode: 'CN123456789',
        expectedTime: new Date(Date.now() + 2 * 60 * 60 * 1000),
        fee: 5,
        description: '中等大小包裹',
        campus: 'main',
        dormBuilding: '1号楼',
        roomNumber: '101',
        contactPhone: '13800138000',
        deliveryAddress: '本部校区 1号楼 101',
        userId: 'student1',
        userName: '张三'
      },
      {
        expressPoint: 'south_jd',
        expressPointName: '南校区京东站点',
        pickupCode: 'JD987654321',
        expectedTime: new Date(Date.now() + 4 * 60 * 60 * 1000),
        fee: 8,
        description: '小件包裹，易碎',
        campus: 'south',
        dormBuilding: '3号楼',
        roomNumber: '205',
        contactPhone: '13900139000',
        deliveryAddress: '南校区 3号楼 205',
        specialRequirements: '请轻拿轻放',
        userId: 'student2',
        userName: '李四'
      }
    ]
    
    sampleOrders.forEach(orderData => {
      orderStore.createOrder(orderData)
    })
  }
})

onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
})
</script>

<style scoped>
.courier-orders-container {
  width: 100%;
  height: 100%;
  padding: 8px;
  overflow-y: auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: #303133;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 15px;
}

.filter-bar {
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.orders-list {
  margin-top: 20px;
}

.order-card {
  margin-bottom: 20px;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.order-info h4 {
  margin: 0 0 5px 0;
  color: #303133;
}

.order-time {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.order-fee {
  text-align: right;
}

.fee-amount {
  display: block;
  font-size: 24px;
  font-weight: bold;
  color: #67c23a;
}

.fee-label {
  font-size: 12px;
  color: #909399;
}

.order-content {
  margin: 15px 0;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.info-icon {
  margin-right: 8px;
  color: #909399;
}

.info-text {
  color: #303133;
}

.special-requirements {
  margin-top: 15px;
  padding: 10px;
  background: #fdf6ec;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.order-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.distance-info {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #909399;
  font-size: 14px;
}

.empty-state {
  text-align: center;
  padding: 40px 0;
}
</style>