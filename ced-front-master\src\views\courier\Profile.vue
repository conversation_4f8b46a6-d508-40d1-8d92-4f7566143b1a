<template>
  <div class="profile-container">
    <el-card>
      <template #header>
        <h3>个人中心</h3>
      </template>
      
      <el-tabs v-model="activeTab">
        <el-tab-pane label="个人信息" name="info">
          <el-form :model="courierInfo" label-width="100px" class="profile-form">
            <el-form-item label="头像">
              <div class="avatar-section">
                <AvatarUpload
                  v-model="courierInfo.avatar"
                  :size="120"
                  :max-size="2"
                  :allowed-types="['image/jpeg', 'image/png']"
                  upload-path="/avatar"
                  alt="代取员头像"
                  :show-actions="false"
                  :show-delete="true"
                  :auto-upload="true"
                  @upload-success="handleAvatarSuccess"
                  @upload-error="handleAvatarError"
                  @delete-success="handleAvatarDelete"
                  @preview="handleAvatarPreview"
                />
                <div class="avatar-tips">
                  <p>支持 JPG、PNG 格式</p>
                  <p>建议尺寸：200x200像素</p>
                  <p>文件大小不超过 2MB</p>
                  <p>点击头像可预览、更换和下载</p>
                </div>
              </div>
            </el-form-item>
            <el-form-item label="姓名">
              <el-input v-model="courierInfo.name" />
            </el-form-item>
            <el-form-item label="学号">
              <el-input v-model="courierInfo.studentId" disabled />
            </el-form-item>
            <el-form-item label="身份证号">
              <el-input v-model="courierInfo.idCard" disabled />
            </el-form-item>
            <el-form-item label="手机号">
              <el-input v-model="courierInfo.phone" />
            </el-form-item>
            <el-form-item label="支付宝账号">
              <el-input v-model="courierInfo.alipayAccount" />
            </el-form-item>
            <el-form-item label="认证状态">
              <el-tag :type="courierInfo.verified ? 'success' : 'warning'">
                {{ courierInfo.verified ? '已认证' : '待审核' }}
              </el-tag>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="updateProfile">保存修改</el-button>
              <el-button @click="showPasswordDialog = true">修改密码</el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        
        <el-tab-pane label="接单设置" name="settings">
          <el-form :model="orderSettings" label-width="120px" class="settings-form">
            <el-form-item label="接单状态">
              <el-switch
                v-model="orderSettings.acceptOrders"
                active-text="开启接单"
                inactive-text="暂停接单"
                @change="toggleOrderStatus"
              />
            </el-form-item>
            <el-form-item label="接单时间段">
              <el-time-picker
                v-model="orderSettings.workTimeStart"
                placeholder="开始时间"
                style="width: 120px; margin-right: 10px;"
              />
              <span>至</span>
              <el-time-picker
                v-model="orderSettings.workTimeEnd"
                placeholder="结束时间"
                style="width: 120px; margin-left: 10px;"
              />
            </el-form-item>
            <el-form-item label="接单范围">
              <el-checkbox-group v-model="orderSettings.campusRange">
                <el-checkbox label="main">本部校区</el-checkbox>
                <el-checkbox label="south">南校区</el-checkbox>
                <el-checkbox label="north">北校区</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="最低接单费用">
              <el-input-number
                v-model="orderSettings.minFee"
                :min="1"
                :max="50"
                :step="0.5"
              />
              <span style="margin-left: 10px;">元</span>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="saveSettings">保存设置</el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        
        <el-tab-pane label="服务评价" name="ratings">
          <div class="ratings-section">
            <div class="rating-summary">
              <div class="rating-score">
                <div class="score-number">{{ ratingStats.averageScore }}</div>
                <el-rate v-model="ratingStats.averageScore" disabled show-score text-color="#ff9900" />
                <div class="score-text">综合评分</div>
              </div>
              <div class="rating-details">
                <div class="rating-item">
                  <span>服务态度</span>
                  <el-progress :percentage="ratingStats.serviceAttitude * 20" />
                  <span>{{ ratingStats.serviceAttitude }}</span>
                </div>
                <div class="rating-item">
                  <span>配送速度</span>
                  <el-progress :percentage="ratingStats.deliverySpeed * 20" />
                  <span>{{ ratingStats.deliverySpeed }}</span>
                </div>
                <div class="rating-item">
                  <span>沟通能力</span>
                  <el-progress :percentage="ratingStats.communication * 20" />
                  <span>{{ ratingStats.communication }}</span>
                </div>
              </div>
            </div>
            
            <div class="rating-comments">
              <h4>用户评价</h4>
              <div v-if="userComments.length === 0" class="empty-comments">
                <el-empty description="暂无评价" />
              </div>
              <div v-else>
                <div v-for="comment in userComments" :key="comment.id" class="comment-item">
                  <div class="comment-header">
                    <div class="user-info">
                      <span class="user-name">{{ comment.userName }}</span>
                      <el-rate v-model="comment.rating" disabled size="small" />
                    </div>
                    <span class="comment-time">{{ comment.time }}</span>
                  </div>
                  <div class="comment-content">{{ comment.content }}</div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="系统通知" name="notifications">
          <div class="notifications-section">
            <div v-if="notifications.length === 0" class="empty-notifications">
              <el-empty description="暂无通知" />
            </div>
            <div v-else>
              <div v-for="notification in notifications" :key="notification.id" class="notification-item">
                <div class="notification-content">
                  <h5>{{ notification.title }}</h5>
                  <p>{{ notification.content }}</p>
                  <span class="notification-time">{{ notification.time }}</span>
                </div>
                <el-button v-if="!notification.read" type="primary" size="small" @click="markAsRead(notification)">
                  标记已读
                </el-button>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>
    
    <!-- 密码修改对话框 -->
    <PasswordChangeDialog
      v-model="showPasswordDialog"
      @success="handlePasswordChangeSuccess"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useAuthStore } from '../../stores/auth.js'
import { ElMessage, ElLoading } from 'element-plus'
import AvatarUpload from '../../components/common/AvatarUpload.vue'
import PasswordChangeDialog from '../../components/common/PasswordChangeDialog.vue'
import { userApi, notificationApi } from '../../request/userApi.js'

const authStore = useAuthStore()
const activeTab = ref('info')
const showPasswordDialog = ref(false)
const loading = ref(false)

const courierInfo = reactive({
  realName: '',
  studentId: '',
  idCard: '',
  phone: '',
  alipayAccount: '',
  wechatAccount: '',
  auditStatus: 1,
  avatar: '',
  rating: 5.0,
  orderCount: 0
})

const orderSettings = reactive({
  acceptOrders: true,
  workTimeStart: '08:00',
  workTimeEnd: '22:00',
  campusRange: ['main', 'south'],
  minFee: 3
})

const ratingStats = reactive({
  averageScore: 4.8,
  serviceAttitude: 4.9,
  deliverySpeed: 4.7,
  communication: 4.8
})

const userComments = ref([])
const notifications = ref([])

// 获取代取员详细资料
const getCourierProfile = async () => {
  try {
    loading.value = true
    const response = await userApi.getUserProfile()
    if (response.code === 200) {
      Object.assign(courierInfo, response.data)
      // 更新评分统计
      ratingStats.averageScore = response.data.rating || 5.0
    }
  } catch (error) {
    console.error('获取代取员资料失败:', error)
    ElMessage.error('获取代取员资料失败')
  } finally {
    loading.value = false
  }
}

// 获取接单设置
const getPickupSettings = async () => {
  try {
    const response = await userApi.getPickupSettings()
    if (response.code === 200) {
      Object.assign(orderSettings, response.data)
    }
  } catch (error) {
    console.error('获取接单设置失败:', error)
    ElMessage.error('获取接单设置失败')
  }
}

// 获取通知列表
const getNotifications = async () => {
  try {
    const response = await notificationApi.getNotifications(1, 10)
    if (response.code === 200) {
      notifications.value = response.data.records.map(notice => ({
        id: notice.noticeId,
        title: notice.title,
        content: notice.content,
        time: notice.createTime,
        read: notice.readStatus === 1
      }))
    }
  } catch (error) {
    console.error('获取通知失败:', error)
    ElMessage.error('获取通知失败')
  }
}

// 更新个人信息
const updateProfile = async () => {
  try {
    const loadingInstance = ElLoading.service({ text: '保存中...' })
    
    const response = await userApi.updateUserProfile({
      realName: courierInfo.realName,
      phone: courierInfo.phone,
      alipayAccount: courierInfo.alipayAccount,
      wechatAccount: courierInfo.wechatAccount,
      avatar: courierInfo.avatar
    })
    
    loadingInstance.close()
    
    if (response.code === 200) {
      ElMessage.success('个人信息更新成功')
      // 更新认证状态中的用户信息
      authStore.updateUserInfo(courierInfo)
    } else {
      ElMessage.error(response.msg || '更新失败')
    }
  } catch (error) {
    console.error('更新个人信息失败:', error)
    ElMessage.error('更新失败，请重试')
  }
}

// 切换接单状态
const toggleOrderStatus = async (status) => {
  orderSettings.acceptOrders = status
  await saveSettings()
  
  if (status) {
    ElMessage.success('已开启接单，您可以接收新订单了')
  } else {
    ElMessage.warning('已暂停接单，您将不会收到新订单')
  }
}

// 保存接单设置
const saveSettings = async () => {
  try {
    const response = await userApi.updatePickupSettings(orderSettings)
    if (response.code === 200) {
      ElMessage.success('接单设置保存成功')
    } else {
      ElMessage.error(response.msg || '保存失败')
    }
  } catch (error) {
    console.error('保存接单设置失败:', error)
    ElMessage.error('保存失败，请重试')
  }
}

// 标记通知为已读
const markAsRead = async (notification) => {
  try {
    const response = await notificationApi.markAsRead(notification.id)
    if (response.code === 200) {
      notification.read = true
      ElMessage.success('已标记为已读')
    }
  } catch (error) {
    console.error('标记已读失败:', error)
    ElMessage.error('操作失败')
  }
}

// 头像上传成功处理
const handleAvatarSuccess = async (file) => {
  try {
    const response = await userApi.uploadAvatar(file)
    if (response.code === 200) {
      courierInfo.avatar = response.data
      ElMessage.success('头像上传成功')
      // 更新认证状态中的用户信息
      authStore.updateUserInfo({ avatar: response.data })
    } else {
      ElMessage.error(response.msg || '头像上传失败')
    }
  } catch (error) {
    console.error('头像上传失败:', error)
    ElMessage.error('头像上传失败，请重试')
  }
}

// 头像上传失败处理
const handleAvatarError = (error) => {
  console.error('头像上传失败:', error)
  ElMessage.error('头像上传失败，请重试')
}

// 头像删除成功处理
const handleAvatarDelete = async () => {
  try {
    const response = await userApi.deleteAvatar()
    if (response.code === 200) {
      courierInfo.avatar = ''
      ElMessage.success('头像删除成功')
    }
  } catch (error) {
    console.error('头像删除失败:', error)
    ElMessage.error('头像删除失败')
  }
}

// 头像预览处理
const handleAvatarPreview = (avatarPath) => {
  console.log('预览头像:', avatarPath)
}

// 获取审核状态名称
const getAuditStatusName = (status) => {
  const statusMap = {
    0: '待审核',
    1: '已认证',
    2: '审核拒绝'
  }
  return statusMap[status] || '未知'
}

// 密码修改成功处理
const handlePasswordChangeSuccess = () => {
  ElMessage.success('密码修改成功，请重新登录')
  // 可以选择自动登出用户
  setTimeout(() => {
    authStore.logout()
    window.location.href = '/login'
  }, 2000)
}

onMounted(async () => {
  // 初始化数据
  await Promise.all([
    getCourierProfile(),
    getPickupSettings(),
    getNotifications()
  ])
})
</script>

<style scoped>
.profile-container {
  width: 100%;
  height: 100%;
  padding: 8px;
  overflow-y: auto;
  display: flex;
  justify-content: center;
}

.profile-container > .el-card {
  width: 100%;
  max-width: 800px;
}

.profile-form, .settings-form {
  max-width: 500px;
}

.ratings-section {
  max-width: 600px;
}

.rating-summary {
  display: flex;
  gap: 40px;
  margin-bottom: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.rating-score {
  text-align: center;
}

.score-number {
  font-size: 48px;
  font-weight: bold;
  color: #e6a23c;
  margin-bottom: 10px;
}

.score-text {
  margin-top: 10px;
  color: #606266;
}

.rating-details {
  flex: 1;
}

.rating-item {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 15px;
}

.rating-item span:first-child {
  width: 80px;
  color: #606266;
}

.rating-item span:last-child {
  width: 30px;
  text-align: center;
  font-weight: bold;
  color: #e6a23c;
}

.rating-comments h4 {
  margin-bottom: 20px;
}

.comment-item {
  padding: 20px;
  border: 1px solid #e6e6e6;
  border-radius: 8px;
  margin-bottom: 15px;
}

.comment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.user-name {
  font-weight: 500;
}

.comment-time {
  font-size: 12px;
  color: #909399;
}

.comment-content {
  color: #606266;
  line-height: 1.5;
}

.notifications-section {
  max-width: 600px;
}

.notification-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 20px;
  border: 1px solid #e6e6e6;
  border-radius: 8px;
  margin-bottom: 15px;
}

.notification-content {
  flex: 1;
}

.notification-content h5 {
  margin: 0 0 8px 0;
  color: #303133;
}

.notification-content p {
  margin: 0 0 8px 0;
  color: #606266;
}

.notification-time {
  font-size: 12px;
  color: #909399;
}

.empty-comments, .empty-notifications {
  text-align: center;
  padding: 40px 0;
}

.avatar-section {
  display: flex;
  align-items: flex-start;
  gap: 20px;
}

.avatar-tips {
  color: #909399;
  font-size: 12px;
  line-height: 1.5;
}

.avatar-tips p {
  margin: 4px 0;
}
</style>