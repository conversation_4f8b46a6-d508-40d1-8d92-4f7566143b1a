<template>
  <div class="earnings-container">
    <el-card>
      <template #header>
        <h3>收入管理</h3>
      </template>
      
      <!-- 收入统计 -->
      <el-row :gutter="20" class="earnings-stats">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">¥{{ earnings.today }}</div>
              <div class="stat-label">今日收入</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">¥{{ earnings.thisWeek }}</div>
              <div class="stat-label">本周收入</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">¥{{ earnings.thisMonth }}</div>
              <div class="stat-label">本月收入</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">¥{{ earnings.total }}</div>
              <div class="stat-label">累计收入</div>
            </div>
          </el-card>
        </el-col>
      </el-row>
      
      <!-- 可提现金额 -->
      <el-card class="withdraw-card" style="margin-top: 20px;">
        <div class="withdraw-content">
          <div class="withdraw-info">
            <h4>可提现金额</h4>
            <div class="withdraw-amount">¥{{ earnings.withdrawable }}</div>
            <p class="withdraw-note">完成订单后的收入将在24小时后可提现</p>
          </div>
          <div class="withdraw-actions">
            <el-button type="primary" size="large" @click="showWithdrawDialog = true" :disabled="earnings.withdrawable <= 0">
              立即提现
            </el-button>
          </div>
        </div>
      </el-card>
      
      <!-- 收入记录 -->
      <el-card style="margin-top: 20px;">
        <template #header>
          <div class="card-header">
            <span>收入记录</span>
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              size="small"
              @change="filterRecords"
            />
          </div>
        </template>
        
        <el-table :data="filteredRecords" style="width: 100%">
          <el-table-column prop="time" label="时间" width="180" />
          <el-table-column prop="orderId" label="订单号" width="120" />
          <el-table-column prop="expressPoint" label="快递点" width="150" />
          <el-table-column prop="amount" label="收入金额" width="100">
            <template #default="scope">
              <span class="income-amount">+¥{{ scope.row.amount }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.status === 'completed' ? 'success' : 'warning'">
                {{ scope.row.status === 'completed' ? '已到账' : '处理中' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="rating" label="用户评分" width="120">
            <template #default="scope">
              <el-rate v-model="scope.row.rating" disabled show-score text-color="#ff9900" />
            </template>
          </el-table-column>
        </el-table>
      </el-card>
      
      <!-- 提现记录 -->
      <el-card style="margin-top: 20px;">
        <template #header>
          <span>提现记录</span>
        </template>
        
        <el-table :data="withdrawRecords" style="width: 100%">
          <el-table-column prop="time" label="申请时间" width="180" />
          <el-table-column prop="amount" label="提现金额" width="120">
            <template #default="scope">
              <span class="withdraw-amount-text">¥{{ scope.row.amount }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="account" label="提现账户" width="200" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="getWithdrawStatusType(scope.row.status)">
                {{ getWithdrawStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="processTime" label="处理时间" width="180" />
        </el-table>
      </el-card>
    </el-card>
    
    <!-- 提现对话框 -->
    <el-dialog v-model="showWithdrawDialog" title="申请提现" width="500px">
      <el-form :model="withdrawForm" :rules="withdrawRules" ref="withdrawFormRef" label-width="100px">
        <el-form-item label="提现金额" prop="amount">
          <el-input-number
            v-model="withdrawForm.amount"
            :min="1"
            :max="earnings.withdrawable"
            :step="0.01"
            style="width: 100%"
          />
          <div class="form-tip">可提现金额：¥{{ earnings.withdrawable }}</div>
        </el-form-item>
        <el-form-item label="提现账户" prop="account">
          <el-select v-model="withdrawForm.account" placeholder="请选择提现账户" style="width: 100%">
            <el-option label="支付宝 (138****8888)" value="alipay_138****8888" />
            <el-option label="微信 (微信号：abc123)" value="wechat_abc123" />
          </el-select>
        </el-form-item>
        <el-form-item label="手续费">
          <span>¥{{ withdrawFee }}</span>
          <div class="form-tip">提现手续费为提现金额的1%，最低1元</div>
        </el-form-item>
        <el-form-item label="实际到账">
          <span class="actual-amount">¥{{ actualAmount }}</span>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showWithdrawDialog = false">取消</el-button>
        <el-button type="primary" @click="handleWithdraw" :loading="withdrawing">确认提现</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useOrderStore } from '../../stores/order.js'
import { useAuthStore } from '../../stores/auth.js'
import { ElMessage } from 'element-plus'

const orderStore = useOrderStore()
const authStore = useAuthStore()

const showWithdrawDialog = ref(false)
const withdrawing = ref(false)
const dateRange = ref([])

const earnings = reactive({
  today: 0,
  thisWeek: 0,
  thisMonth: 0,
  total: 0,
  withdrawable: 156.50
})

const earningsRecords = ref([
  {
    time: '2024-01-15 16:30',
    orderId: 'ORD001',
    expressPoint: '本部菜鸟驿站',
    amount: 5,
    status: 'completed',
    rating: 5
  },
  {
    time: '2024-01-15 14:20',
    orderId: 'ORD002',
    expressPoint: '南校区京东站点',
    amount: 8,
    status: 'completed',
    rating: 4
  },
  {
    time: '2024-01-14 11:45',
    orderId: 'ORD003',
    expressPoint: '本部顺丰站点',
    amount: 6,
    status: 'processing',
    rating: 0
  }
])

const withdrawRecords = ref([
  {
    time: '2024-01-10 09:30',
    amount: 100,
    account: '支付宝 (138****8888)',
    status: 'completed',
    processTime: '2024-01-10 15:20'
  },
  {
    time: '2024-01-05 14:15',
    amount: 80,
    account: '微信 (微信号：abc123)',
    status: 'processing',
    processTime: '-'
  }
])

const withdrawForm = reactive({
  amount: 0,
  account: ''
})

const withdrawRules = {
  amount: [{ required: true, message: '请输入提现金额', trigger: 'blur' }],
  account: [{ required: true, message: '请选择提现账户', trigger: 'change' }]
}

const withdrawFormRef = ref()

const filteredRecords = computed(() => {
  if (!dateRange.value || dateRange.value.length !== 2) {
    return earningsRecords.value
  }
  
  const [startDate, endDate] = dateRange.value
  return earningsRecords.value.filter(record => {
    const recordDate = new Date(record.time)
    return recordDate >= startDate && recordDate <= endDate
  })
})

const withdrawFee = computed(() => {
  const fee = withdrawForm.amount * 0.01
  return Math.max(fee, 1).toFixed(2)
})

const actualAmount = computed(() => {
  return (withdrawForm.amount - parseFloat(withdrawFee.value)).toFixed(2)
})

const getWithdrawStatusType = (status) => {
  const statusMap = {
    'processing': 'warning',
    'completed': 'success',
    'failed': 'danger'
  }
  return statusMap[status] || 'info'
}

const getWithdrawStatusText = (status) => {
  const statusMap = {
    'processing': '处理中',
    'completed': '已完成',
    'failed': '失败'
  }
  return statusMap[status] || '未知'
}

const filterRecords = () => {
  // 过滤逻辑已在computed中实现
}

const handleWithdraw = async () => {
  if (!withdrawFormRef.value) return
  
  try {
    await withdrawFormRef.value.validate()
    withdrawing.value = true
    
    // 模拟提现申请
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 添加提现记录
    withdrawRecords.value.unshift({
      time: new Date().toLocaleString(),
      amount: withdrawForm.amount,
      account: withdrawForm.account,
      status: 'processing',
      processTime: '-'
    })
    
    // 更新可提现金额
    earnings.withdrawable -= withdrawForm.amount
    
    showWithdrawDialog.value = false
    ElMessage.success('提现申请提交成功，预计1-3个工作日到账')
  } catch (error) {
    console.error('提现失败:', error)
  } finally {
    withdrawing.value = false
  }
}

onMounted(() => {
  // 计算收入统计
  const completedOrders = orderStore.getOrdersByCourier(authStore.user?.id)
    .filter(order => order.status === orderStore.ORDER_STATUS.COMPLETED)
  
  const today = new Date().toDateString()
  const thisWeekStart = new Date()
  thisWeekStart.setDate(thisWeekStart.getDate() - thisWeekStart.getDay())
  
  const thisMonthStart = new Date()
  thisMonthStart.setDate(1)
  
  earnings.today = completedOrders
    .filter(order => new Date(order.updateTime).toDateString() === today)
    .reduce((total, order) => total + order.fee, 0)
  
  earnings.thisWeek = completedOrders
    .filter(order => new Date(order.updateTime) >= thisWeekStart)
    .reduce((total, order) => total + order.fee, 0)
  
  earnings.thisMonth = completedOrders
    .filter(order => new Date(order.updateTime) >= thisMonthStart)
    .reduce((total, order) => total + order.fee, 0)
  
  earnings.total = completedOrders.reduce((total, order) => total + order.fee, 0)
})
</script>

<style scoped>
.earnings-container {
  width: 100%;
  height: 100%;
  padding: 8px;
  overflow-y: auto;
}

.earnings-stats {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
}

.stat-content {
  padding: 20px 0;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #67c23a;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.withdraw-card {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  color: #1e293b;
  border: 1px solid #e2e8f0;
}

.withdraw-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
}

.withdraw-info h4 {
  margin: 0 0 10px 0;
  color: #374151;
  font-weight: 600;
}

.withdraw-amount {
  font-size: 32px;
  font-weight: bold;
  margin: 10px 0;
  color: #10b981;
}

.withdraw-note {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.income-amount {
  color: #67c23a;
  font-weight: bold;
}

.withdraw-amount-text {
  color: #f56c6c;
  font-weight: bold;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.actual-amount {
  font-size: 18px;
  font-weight: bold;
  color: #67c23a;
}
</style>