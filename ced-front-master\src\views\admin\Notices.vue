<template>
  <div class="notices-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <div class="header-title">
            <el-icon><Bell /></el-icon>
            <h3>系统公告管理</h3>
          </div>
          <div class="header-actions">
            <el-button type="primary" @click="showCreateDialog = true">
              <el-icon><Plus /></el-icon>
              发布公告
            </el-button>
          </div>
        </div>
      </template>
      
      <div class="notices-list">
        <div v-if="notices.length === 0" class="empty-state">
          <el-empty description="暂无公告" :image-size="100" />
        </div>
        
        <div v-else>
          <div 
            v-for="notice in notices" 
            :key="notice.id" 
            class="notice-item"
            :class="{ published: notice.status === 'published' }"
          >
            <div class="notice-header">
              <div class="notice-title">
                {{ notice.title }}
              </div>
              <div class="notice-meta">
                <el-tag :type="getStatusType(notice.status)" size="small">
                  {{ getStatusText(notice.status) }}
                </el-tag>
                <el-tag :type="getNoticeType(notice.type)" size="small">
                  {{ getNoticeTypeText(notice.type) }}
                </el-tag>
                <span class="notice-time">{{ formatTime(notice.time) }}</span>
              </div>
            </div>
            <div class="notice-content">
              {{ notice.content }}
            </div>
            <div class="notice-stats">
              <span class="stat-item">阅读量：{{ notice.readCount }}</span>
              <span class="stat-item">目标用户：{{ getTargetText(notice.target) }}</span>
            </div>
            <div class="notice-actions">
              <el-button type="primary" text size="small" @click="editNotice(notice)">
                编辑
              </el-button>
              <el-button 
                v-if="notice.status === 'draft'" 
                type="success" 
                text 
                size="small"
                @click="publishNotice(notice)"
              >
                发布
              </el-button>
              <el-button 
                v-if="notice.status === 'published'" 
                type="warning" 
                text 
                size="small"
                @click="unpublishNotice(notice)"
              >
                撤回
              </el-button>
              <el-button type="danger" text size="small" @click="deleteNotice(notice)">
                删除
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </el-card>
    
    <!-- 创建/编辑公告对话框 -->
    <el-dialog v-model="showCreateDialog" :title="editingNotice ? '编辑公告' : '发布公告'" width="600px">
      <el-form :model="noticeForm" :rules="noticeRules" ref="noticeFormRef" label-width="100px">
        <el-form-item label="公告标题" prop="title">
          <el-input v-model="noticeForm.title" placeholder="请输入公告标题" />
        </el-form-item>
        <el-form-item label="公告类型" prop="type">
          <el-select v-model="noticeForm.type" placeholder="请选择公告类型" style="width: 100%">
            <el-option label="系统通知" value="system" />
            <el-option label="功能更新" value="feature" />
            <el-option label="重要公告" value="important" />
            <el-option label="节假日通知" value="holiday" />
            <el-option label="安全提醒" value="security" />
          </el-select>
        </el-form-item>
        <el-form-item label="目标用户" prop="target">
          <el-checkbox-group v-model="noticeForm.target">
            <el-checkbox label="student">学生</el-checkbox>
            <el-checkbox label="courier">代取员</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="公告内容" prop="content">
          <el-input 
            v-model="noticeForm.content" 
            type="textarea" 
            :rows="4" 
            placeholder="请输入公告内容"
          />
        </el-form-item>
        <el-form-item label="发布状态" prop="status">
          <el-radio-group v-model="noticeForm.status">
            <el-radio label="draft">保存草稿</el-radio>
            <el-radio label="published">立即发布</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="saveNotice" :loading="saving">
          {{ editingNotice ? '更新' : '保存' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'
import { Bell, Plus } from '@element-plus/icons-vue'
import { adminApi } from '../../request/adminApi.js'

const showCreateDialog = ref(false)
const editingNotice = ref(null)
const saving = ref(false)
const loading = ref(false)

// 公告数据
const notices = ref([])

const noticeForm = reactive({
  title: '',
  content: '',
  type: '',
  target: [],
  status: 'draft'
})

const noticeRules = {
  title: [{ required: true, message: '请输入公告标题', trigger: 'blur' }],
  content: [{ required: true, message: '请输入公告内容', trigger: 'blur' }],
  type: [{ required: true, message: '请选择公告类型', trigger: 'change' }],
  target: [{ required: true, message: '请选择目标用户', trigger: 'change' }]
}

const noticeFormRef = ref()

const formatTime = (time) => {
  if (!time) return ''
  return new Date(time).toLocaleString()
}

const getStatusType = (status) => {
  return status === 'published' ? 'success' : 'info'
}

const getStatusText = (status) => {
  return status === 'published' ? '已发布' : '草稿'
}

const getNoticeType = (type) => {
  const typeMap = {
    system: 'info',
    feature: 'success',
    important: 'warning',
    holiday: 'primary',
    security: 'danger'
  }
  return typeMap[type] || 'info'
}

const getNoticeTypeText = (type) => {
  const typeMap = {
    system: '系统',
    feature: '功能',
    important: '重要',
    holiday: '节假日',
    security: '安全'
  }
  return typeMap[type] || '通知'
}

const getTargetText = (target) => {
  const targetMap = {
    student: '学生',
    courier: '代取员'
  }
  return target.map(t => targetMap[t]).join('、')
}

// 编辑公告
const editNotice = (notice) => {
  editingNotice.value = notice
  Object.assign(noticeForm, {
    title: notice.title,
    content: notice.content,
    type: notice.type,
    target: [...notice.target],
    status: notice.status
  })
  showCreateDialog.value = true
}

// 保存公告
const saveNotice = async () => {
  if (!noticeFormRef.value) return
  
  try {
    await noticeFormRef.value.validate()
    saving.value = true
    
    let response
    
    if (editingNotice.value) {
      // 更新现有公告
      response = await adminApi.updateNotice(editingNotice.value.id, noticeForm)
    } else {
      // 创建新公告
      response = await adminApi.createNotice(noticeForm)
    }
    
    if (response.code === 200) {
      ElMessage.success(editingNotice.value ? '公告更新成功' : '公告创建成功')
      fetchNotices() // 刷新列表
      showCreateDialog.value = false
      resetForm()
    } else {
      ElMessage.error(response.msg || (editingNotice.value ? '更新失败' : '创建失败'))
    }
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败，请重试')
  } finally {
    saving.value = false
  }
}

// 发布公告
const publishNotice = async (notice) => {
  try {
    await ElMessageBox.confirm('确定要发布这条公告吗？', '发布公告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'success'
    })
    
    const loadingInstance = ElLoading.service({ text: '正在发布公告...' })
    
    try {
      const response = await adminApi.publishNotice(notice.id)
      
      if (response.code === 200) {
        ElMessage.success('公告发布成功')
        fetchNotices() // 刷新列表
      } else {
        ElMessage.error(response.msg || '发布失败')
      }
    } catch (error) {
      console.error('发布失败:', error)
      ElMessage.error('发布失败，请重试')
    } finally {
      loadingInstance.close()
    }
  } catch {
    // 用户取消
  }
}

// 撤回公告
const unpublishNotice = async (notice) => {
  try {
    await ElMessageBox.confirm('确定要撤回这条公告吗？', '撤回公告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const loadingInstance = ElLoading.service({ text: '正在撤回公告...' })
    
    try {
      const response = await adminApi.unpublishNotice(notice.id)
      
      if (response.code === 200) {
        ElMessage.success('公告已撤回')
        fetchNotices() // 刷新列表
      } else {
        ElMessage.error(response.msg || '撤回失败')
      }
    } catch (error) {
      console.error('撤回失败:', error)
      ElMessage.error('撤回失败，请重试')
    } finally {
      loadingInstance.close()
    }
  } catch {
    // 用户取消
  }
}

// 删除公告
const deleteNotice = async (notice) => {
  try {
    await ElMessageBox.confirm('确定要删除这条公告吗？删除后无法恢复。', '删除公告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'danger'
    })
    
    const loadingInstance = ElLoading.service({ text: '正在删除公告...' })
    
    try {
      const response = await adminApi.deleteNotice(notice.id)
      
      if (response.code === 200) {
        ElMessage.success('公告删除成功')
        fetchNotices() // 刷新列表
      } else {
        ElMessage.error(response.msg || '删除失败')
      }
    } catch (error) {
      console.error('删除失败:', error)
      ElMessage.error('删除失败，请重试')
    } finally {
      loadingInstance.close()
    }
  } catch {
    // 用户取消
  }
}

// 重置表单
const resetForm = () => {
  editingNotice.value = null
  Object.assign(noticeForm, {
    title: '',
    content: '',
    type: '',
    target: [],
    status: 'draft'
  })
}

// 获取公告列表
const fetchNotices = async () => {
  loading.value = true
  
  try {
    const response = await adminApi.getNotices()
    
    if (response.code === 200) {
      notices.value = response.data
    } else {
      ElMessage.error(response.msg || '获取公告列表失败')
    }
  } catch (error) {
    console.error('获取公告列表失败:', error)
    ElMessage.error('获取公告列表失败，请重试')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  // 加载公告数据
  fetchNotices()
})
</script>

<style scoped>
.notices-container {
  width: 100%;
  height: 100%;
  padding: 8px;
  overflow-y: auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-title h3 {
  margin: 0;
  color: #1a1a1a;
  font-size: 18px;
  font-weight: 600;
}

.notices-list {
  max-height: calc(100vh - 200px);
  overflow-y: auto;
}

.notice-item {
  padding: 20px;
  border: 1px solid #f0f0f0;
  border-radius: 12px;
  margin-bottom: 16px;
  background: #ffffff;
  transition: all 0.3s ease;
}

.notice-item:hover {
  border-color: #f59e0b;
  box-shadow: 0 2px 8px rgba(245, 158, 11, 0.1);
  transform: translateY(-1px);
}

.notice-item.published {
  border-left: 4px solid #10b981;
  background: #f0fdf4;
}

.notice-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.notice-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
  flex: 1;
}

.notice-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.notice-time {
  font-size: 12px;
  color: #6b7280;
}

.notice-content {
  color: #4b5563;
  line-height: 1.6;
  margin-bottom: 12px;
  font-size: 14px;
}

.notice-stats {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
  font-size: 12px;
  color: #6b7280;
}

.stat-item {
  display: flex;
  align-items: center;
}

.notice-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.empty-state {
  text-align: center;
  padding: 60px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .notice-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .notice-meta {
    width: 100%;
    justify-content: space-between;
  }
  
  .notice-actions {
    justify-content: flex-start;
  }
}
</style>