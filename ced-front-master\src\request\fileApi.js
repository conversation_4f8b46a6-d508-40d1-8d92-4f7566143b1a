import request from './request'

/**
 * 上传文件
 * @param {Object} options - 上传选项
 * @param {File} options.file - 要上传的文件
 * @param {string} options.path - 上传路径
 * @returns {Promise} 上传结果
 */
export const uploadFileApi = (options) => {
  const { file, path } = options
  const formData = new FormData()
  formData.append('file', file)
  
  return request.post(path || '/file/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 下载文件
 * @param {string} filePath - 文件路径
 * @returns {Promise<Blob>} 文件Blob对象
 */
export const downloadFile = async (filePath) => {
  // 如果是完整URL，直接使用
  if (filePath.startsWith('http') || filePath.startsWith('blob:')) {
    try {
      const response = await fetch(filePath)
      return await response.blob()
    } catch (error) {
      console.error('下载文件失败:', error)
      throw error
    }
  }
  
  // 如果是相对路径，添加API基础路径
  const baseUrl = import.meta.env.VITE_API_BASE_URL || ''
  const fullPath = filePath.startsWith('/') ? `${baseUrl}${filePath}` : `${baseUrl}/${filePath}`
  
  try {
    const response = await fetch(fullPath)
    if (!response.ok) {
      throw new Error(`下载失败: ${response.status} ${response.statusText}`)
    }
    return await response.blob()
  } catch (error) {
    console.error('下载文件失败:', error)
    throw error
  }
}

/**
 * 删除文件
 * @param {Object} options - 删除选项
 * @param {string} options.path - 文件路径
 * @returns {Promise} 删除结果
 */
export const deleteFileApi = (options) => {
  const { path } = options
  return request.delete('/file/delete', {
    params: { path }
  })
}