<template>
  <div class="profile-container">
    <el-card>
      <template #header>
        <h3>管理员中心</h3>
      </template>
      
      <el-tabs v-model="activeTab">
        <el-tab-pane label="个人信息" name="info">
          <el-form :model="adminInfo" label-width="100px" class="profile-form">
            <el-form-item label="头像">
              <div class="avatar-section">
              <AvatarUpload
                v-model="adminInfo.avatar"
                :size="120"
                :max-size="2"
                :allowed-types="['image/jpeg', 'image/png']"
                upload-path="/avatar"
                alt="管理员头像"
                :show-actions="false"
                :show-delete="true"
                :auto-upload="true"
                @upload-success="handleAvatarSuccess"
                @upload-error="handleAvatarError"
                @delete-success="handleAvatarDelete"
                @preview="handleAvatarPreview"
              />
                <div class="avatar-tips">
                  <p>支持 JPG、PNG 格式</p>
                  <p>建议尺寸：200x200像素</p>
                  <p>文件大小不超过 2MB</p>
                </div>
              </div>
            </el-form-item>
            <el-form-item label="姓名">
              <el-input v-model="adminInfo.realName" />
            </el-form-item>
            <el-form-item label="用户名">
              <el-input v-model="adminInfo.username" disabled />
            </el-form-item>
            <el-form-item label="手机号">
              <el-input v-model="adminInfo.phone" />
            </el-form-item>
            <el-form-item label="邮箱">
              <el-input v-model="adminInfo.email" />
            </el-form-item>
            <el-form-item label="角色">
              <el-tag type="danger">系统管理员</el-tag>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="updateProfile">保存修改</el-button>
              <el-button @click="showPasswordDialog = true">修改密码</el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        
        <el-tab-pane label="系统统计" name="statistics">
          <div class="statistics-section">
            <el-row :gutter="20">
              <el-col :span="6">
                <el-card class="stat-card">
                  <div class="stat-content">
                    <div class="stat-number">{{ systemStats.totalUsers }}</div>
                    <div class="stat-label">总用户数</div>
                  </div>
                  <el-icon class="stat-icon"><User /></el-icon>
                </el-card>
              </el-col>
              <el-col :span="6">
                <el-card class="stat-card">
                  <div class="stat-content">
                    <div class="stat-number">{{ systemStats.totalOrders }}</div>
                    <div class="stat-label">总订单数</div>
                  </div>
                  <el-icon class="stat-icon"><Document /></el-icon>
                </el-card>
              </el-col>
              <el-col :span="6">
                <el-card class="stat-card">
                  <div class="stat-content">
                    <div class="stat-number">{{ systemStats.activeCouriers }}</div>
                    <div class="stat-label">活跃代取员</div>
                  </div>
                  <el-icon class="stat-icon"><Truck /></el-icon>
                </el-card>
              </el-col>
              <el-col :span="6">
                <el-card class="stat-card">
                  <div class="stat-content">
                    <div class="stat-number">¥{{ systemStats.totalRevenue }}</div>
                    <div class="stat-label">平台收入</div>
                  </div>
                  <el-icon class="stat-icon"><Money /></el-icon>
                </el-card>
              </el-col>
            </el-row>
            
            <el-row :gutter="20" style="margin-top: 20px;">
              <el-col :span="12">
                <el-card>
                  <template #header>
                    <h4>今日数据</h4>
                  </template>
                  <div class="today-stats">
                    <div class="today-item">
                      <span>新增用户</span>
                      <span class="today-number">{{ systemStats.todayNewUsers }}</span>
                    </div>
                    <div class="today-item">
                      <span>新增订单</span>
                      <span class="today-number">{{ systemStats.todayNewOrders }}</span>
                    </div>
                    <div class="today-item">
                      <span>完成订单</span>
                      <span class="today-number">{{ systemStats.todayCompletedOrders }}</span>
                    </div>
                  </div>
                </el-card>
              </el-col>
              <el-col :span="12">
                <el-card>
                  <template #header>
                    <h4>待处理事项</h4>
                  </template>
                  <div class="pending-tasks">
                    <div class="task-item">
                      <span>待审核代取员</span>
                      <el-badge :value="systemStats.pendingCouriers" class="task-badge">
                        <el-button size="small" @click="$router.push('/admin/couriers')">查看</el-button>
                      </el-badge>
                    </div>
                    <div class="task-item">
                      <span>用户举报</span>
                      <el-badge :value="systemStats.userReports" class="task-badge">
                        <el-button size="small">查看</el-button>
                      </el-badge>
                    </div>
                    <div class="task-item">
                      <span>系统异常</span>
                      <el-badge :value="systemStats.systemErrors" class="task-badge">
                        <el-button size="small">查看</el-button>
                      </el-badge>
                    </div>
                  </div>
                </el-card>
              </el-col>
            </el-row>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="操作日志" name="logs">
          <div class="logs-section">
            <el-table :data="operationLogs" style="width: 100%">
              <el-table-column prop="time" label="操作时间" width="180" />
              <el-table-column prop="action" label="操作类型" width="120">
                <template #default="scope">
                  <el-tag :type="getActionType(scope.row.action)">
                    {{ scope.row.action }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="target" label="操作对象" width="150" />
              <el-table-column prop="description" label="操作描述" />
              <el-table-column prop="result" label="结果" width="80">
                <template #default="scope">
                  <el-tag :type="scope.row.result === '成功' ? 'success' : 'danger'">
                    {{ scope.row.result }}
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>
            
            <el-pagination
              v-model:current-page="logPagination.current"
              v-model:page-size="logPagination.size"
              :page-sizes="[10, 20, 50, 100]"
              :total="logPagination.total"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleLogSizeChange"
              @current-change="handleLogCurrentChange"
              style="margin-top: 20px; text-align: right;"
            />
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="系统通知" name="notifications">
          <div class="notifications-section">
            <div v-if="notifications.length === 0" class="empty-notifications">
              <el-empty description="暂无通知" />
            </div>
            <div v-else>
              <div v-for="notification in notifications" :key="notification.id" class="notification-item">
                <div class="notification-content">
                  <h5>{{ notification.title }}</h5>
                  <p>{{ notification.content }}</p>
                  <span class="notification-time">{{ notification.time }}</span>
                </div>
                <el-button v-if="!notification.read" type="primary" size="small" @click="markAsRead(notification)">
                  标记已读
                </el-button>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>
    
    <!-- 密码修改对话框 -->
    <PasswordChangeDialog
      v-model="showPasswordDialog"
      @success="handlePasswordChangeSuccess"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useAuthStore } from '../../stores/auth.js'
import { ElMessage, ElLoading } from 'element-plus'
import { User, Document, Truck, Money } from '@element-plus/icons-vue'
import AvatarUpload from '../../components/common/AvatarUpload.vue'
import PasswordChangeDialog from '../../components/common/PasswordChangeDialog.vue'
import { userApi, notificationApi } from '../../request/userApi.js'
import { adminApi } from '../../request/adminApi.js'

const authStore = useAuthStore()
const activeTab = ref('info')
const showPasswordDialog = ref(false)
const loading = ref(false)

const adminInfo = reactive({
  realName: '',
  username: '',
  phone: '',
  email: '',
  avatar: ''
})

const systemStats = reactive({
  totalUsers: 0,
  totalOrders: 0,
  activeCouriers: 0,
  totalRevenue: 0,
  todayNewUsers: 0,
  todayNewOrders: 0,
  todayCompletedOrders: 0,
  pendingCouriers: 0,
  userReports: 0,
  systemErrors: 0
})

const operationLogs = ref([])

const logPagination = reactive({
  current: 1,
  size: 10,
  total: 100
})

const notifications = ref([])

// 获取管理员详细资料
const getAdminProfile = async () => {
  try {
    loading.value = true
    const response = await userApi.getUserProfile()
    if (response.code === 200) {
      Object.assign(adminInfo, response.data)
    } else {
      ElMessage.error(response.msg || '获取管理员资料失败')
    }
  } catch (error) {
    console.error('获取管理员资料失败:', error)
    ElMessage.error('获取管理员资料失败')
  } finally {
    loading.value = false
  }
}

// 获取通知列表
const getNotifications = async () => {
  try {
    const response = await notificationApi.getNotifications(1, 10)
    if (response.code === 200) {
      notifications.value = response.data.records.map(notice => ({
        id: notice.noticeId,
        title: notice.title,
        content: notice.content,
        time: notice.createTime,
        read: notice.readStatus === 1
      }))
    } else {
      ElMessage.error(response.msg || '获取通知失败')
    }
  } catch (error) {
    console.error('获取通知失败:', error)
    ElMessage.error('获取通知失败')
  }
}

// 更新个人信息
const updateProfile = async () => {
  try {
    const loadingInstance = ElLoading.service({ text: '保存中...' })
    
    const response = await userApi.updateUserProfile({
      realName: adminInfo.realName,
      phone: adminInfo.phone,
      email: adminInfo.email,
      avatar: adminInfo.avatar
    })
    
    loadingInstance.close()
    
    if (response.code === 200) {
      ElMessage.success('个人信息更新成功')
      authStore.updateUserInfo(adminInfo)
    } else {
      ElMessage.error(response.msg || '更新失败')
    }
  } catch (error) {
    console.error('更新个人信息失败:', error)
    ElMessage.error('更新失败，请重试')
  }
}

// 标记通知为已读
const markAsRead = async (notification) => {
  try {
    const response = await notificationApi.markAsRead(notification.id)
    if (response.code === 200) {
      notification.read = true
      ElMessage.success('已标记为已读')
    }
  } catch (error) {
    console.error('标记已读失败:', error)
    ElMessage.error('操作失败')
  }
}

// 头像处理函数
const handleAvatarSuccess = async (file) => {
  try {
    const response = await userApi.uploadAvatar(file)
    if (response.code === 200) {
      adminInfo.avatar = response.data
      ElMessage.success('头像上传成功')
      // 更新认证状态中的用户信息
      authStore.updateUserInfo({ avatar: response.data })
    } else {
      ElMessage.error(response.msg || '头像上传失败')
    }
  } catch (error) {
    console.error('头像上传失败:', error)
    ElMessage.error('头像上传失败，请重试')
  }
}

const handleAvatarError = (error) => {
  console.error('头像上传失败:', error)
  ElMessage.error('头像上传失败，请重试')
}

const handleAvatarDelete = async () => {
  try {
    const response = await userApi.deleteAvatar()
    if (response.code === 200) {
      adminInfo.avatar = ''
      ElMessage.success('头像删除成功')
    }
  } catch (error) {
    console.error('头像删除失败:', error)
    ElMessage.error('头像删除失败')
  }
}

const handleAvatarPreview = (avatarPath) => {
  console.log('预览头像:', avatarPath)
}

// 密码修改成功处理
const handlePasswordChangeSuccess = () => {
  ElMessage.success('密码修改成功，请重新登录')
  setTimeout(() => {
    authStore.logout()
    window.location.href = '/login'
  }, 2000)
}

// 获取操作类型样式
const getActionType = (action) => {
  const typeMap = {
    '用户审核': 'success',
    '系统配置': 'primary',
    '用户管理': 'warning',
    '数据导出': 'info'
  }
  return typeMap[action] || 'default'
}

// 日志分页处理
const handleLogSizeChange = (size) => {
  logPagination.size = size
  getOperationLogs()
}

const handleLogCurrentChange = (current) => {
  logPagination.current = current
  getOperationLogs()
}

// 获取系统统计数据
const getSystemStatistics = async () => {
  try {
    const response = await adminApi.getSystemStats()
    if (response.code === 200) {
      Object.assign(systemStats, response.data)
    } else {
      ElMessage.error(response.msg || '获取系统统计数据失败')
    }
  } catch (error) {
    console.error('获取系统统计数据失败:', error)
    ElMessage.error('获取系统统计数据失败')
  }
}

// 获取今日数据
const getTodayStats = async () => {
  try {
    const response = await adminApi.getTodayStats()
    if (response.code === 200) {
      systemStats.todayNewUsers = response.data.newUsers
      systemStats.todayNewOrders = response.data.newOrders
      systemStats.todayCompletedOrders = response.data.completedOrders
    } else {
      ElMessage.error(response.msg || '获取今日数据失败')
    }
  } catch (error) {
    console.error('获取今日数据失败:', error)
    ElMessage.error('获取今日数据失败')
  }
}

// 获取待处理事项
const getPendingTasks = async () => {
  try {
    const response = await adminApi.getPendingTasks()
    if (response.code === 200) {
      systemStats.pendingCouriers = response.data.pendingCouriers
      systemStats.userReports = response.data.userReports
      systemStats.systemErrors = response.data.systemErrors
    } else {
      ElMessage.error(response.msg || '获取待处理事项失败')
    }
  } catch (error) {
    console.error('获取待处理事项失败:', error)
    ElMessage.error('获取待处理事项失败')
  }
}

// 获取操作日志
const getOperationLogs = async () => {
  try {
    const response = await adminApi.getOperationLogs(logPagination.current, logPagination.size)
    if (response.code === 200) {
      operationLogs.value = response.data.records
      logPagination.total = response.data.total
    } else {
      ElMessage.error(response.msg || '获取操作日志失败')
    }
  } catch (error) {
    console.error('获取操作日志失败:', error)
    ElMessage.error('获取操作日志失败')
  }
}

onMounted(async () => {
  // 初始化数据
  await Promise.all([
    getAdminProfile(),
    getNotifications(),
    getSystemStatistics(),
    getTodayStats(),
    getPendingTasks(),
    getOperationLogs()
  ])
})
</script>

<style scoped>
.profile-container {
  width: 100%;
  height: 100%;
  padding: 8px;
  overflow-y: auto;
  display: flex;
  justify-content: center;
}

.profile-container > .el-card {
  width: 100%;
  max-width: 1000px;
}

.profile-form {
  max-width: 500px;
}

.avatar-section {
  display: flex;
  align-items: flex-start;
  gap: 20px;
}

.avatar-tips {
  color: #909399;
  font-size: 12px;
  line-height: 1.5;
}

.avatar-tips p {
  margin: 4px 0;
}

.statistics-section {
  width: 100%;
}

.stat-card {
  position: relative;
  overflow: hidden;
}

.stat-content {
  text-align: center;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 8px;
}

.stat-label {
  color: #606266;
  font-size: 14px;
}

.stat-icon {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 40px;
  color: #e6e6e6;
}

.today-stats, .pending-tasks {
  padding: 10px 0;
}

.today-item, .task-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #f0f0f0;
}

.today-item:last-child, .task-item:last-child {
  border-bottom: none;
}

.today-number {
  font-weight: bold;
  color: #409eff;
}

.task-badge {
  margin-left: 10px;
}

.logs-section {
  width: 100%;
}

.notifications-section {
  max-width: 600px;
}

.notification-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 20px;
  border: 1px solid #e6e6e6;
  border-radius: 8px;
  margin-bottom: 15px;
}

.notification-content {
  flex: 1;
}

.notification-content h5 {
  margin: 0 0 8px 0;
  color: #303133;
}

.notification-content p {
  margin: 0 0 8px 0;
  color: #606266;
}

.notification-time {
  font-size: 12px;
  color: #909399;
}

.empty-notifications {
  text-align: center;
  padding: 40px 0;
}
</style>