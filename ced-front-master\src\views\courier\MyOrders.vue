<template>
  <div class="my-orders-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <h3>我的订单</h3>
          <div class="stats">
            <el-statistic title="今日接单" :value="todayOrders" />
            <el-statistic title="今日收入" :value="todayEarnings" prefix="¥" />
          </div>
        </div>
      </template>
      
      <el-tabs v-model="activeTab">
        <el-tab-pane label="进行中" name="processing" />
        <el-tab-pane label="已完成" name="completed" />
        <el-tab-pane label="全部订单" name="all" />
      </el-tabs>
      
      <div class="orders-list">
        <div v-if="filteredOrders.length === 0" class="empty-state">
          <el-empty description="暂无订单" :image-size="100">
            <el-button type="primary" @click="$router.push('/courier/orders')">
              去接单
            </el-button>
          </el-empty>
        </div>
        
        <div v-else>
          <el-card v-for="order in filteredOrders" :key="order.id" class="order-card" shadow="hover">
            <div class="order-header">
              <div class="order-info">
                <h4>{{ order.expressPointName }}</h4>
                <p class="order-time">接单时间：{{ formatTime(order.updateTime) }}</p>
              </div>
              <div class="order-status">
                <el-tag :type="getStatusType(order.status)" size="large">
                  {{ getStatusText(order.status) }}
                </el-tag>
                <div class="fee-amount">¥{{ order.fee }}</div>
              </div>
            </div>
            
            <el-divider />
            
            <div class="order-content">
              <el-row :gutter="20">
                <el-col :span="12">
                  <div class="info-item">
                    <span class="label">取件码：</span>
                    <span class="value">{{ order.pickupCode }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">用户：</span>
                    <span class="value">{{ order.userName }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">联系方式：</span>
                    <span class="value">{{ hidePhone(order.contactPhone) }}</span>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="info-item">
                    <span class="label">送达地址：</span>
                    <span class="value">{{ order.deliveryAddress }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">期望送达：</span>
                    <span class="value">{{ formatTime(order.expectedTime) }}</span>
                  </div>
                  <div class="info-item" v-if="order.description">
                    <span class="label">快递描述：</span>
                    <span class="value">{{ order.description }}</span>
                  </div>
                </el-col>
              </el-row>
              
              <div v-if="order.specialRequirements" class="special-requirements">
                <el-tag type="warning" size="small">特殊要求</el-tag>
                <span>{{ order.specialRequirements }}</span>
              </div>
            </div>
            
            <el-divider />
            
            <div class="order-actions">
              <div class="action-buttons">
                <template v-if="order.status === orderStore.ORDER_STATUS.ACCEPTED">
                  <el-button type="success" @click="handlePickupConfirm(order)">
                    确认取件
                  </el-button>
                  <el-button type="primary" plain @click="handleContactUser(order)">
                    联系用户
                  </el-button>
                </template>
                
                <template v-else-if="order.status === orderStore.ORDER_STATUS.PICKED_UP">
                  <el-button type="primary" @click="handleStartDelivery(order)">
                    开始配送
                  </el-button>
                  <el-button plain @click="handleUploadPhoto(order)">
                    上传取件照片
                  </el-button>
                </template>
                
                <template v-else-if="order.status === orderStore.ORDER_STATUS.DELIVERING">
                  <el-button type="success" @click="handleDeliveryConfirm(order)">
                    确认送达
                  </el-button>
                  <el-button type="primary" plain @click="handleContactUser(order)">
                    联系用户
                  </el-button>
                </template>
                
                <template v-else-if="order.status === orderStore.ORDER_STATUS.DELIVERED">
                  <el-button type="info" disabled>等待用户确认收货</el-button>
                </template>
                
                <template v-else-if="order.status === orderStore.ORDER_STATUS.COMPLETED">
                  <el-button type="primary" plain @click="handleViewRating(order)">
                    查看评价
                  </el-button>
                </template>
              </div>
              
              <div class="order-timeline">
                <el-steps :active="getStepActive(order.status)" size="small" finish-status="success">
                  <el-step title="已接单" />
                  <el-step title="已取件" />
                  <el-step title="配送中" />
                  <el-step title="已送达" />
                  <el-step title="已完成" />
                </el-steps>
              </div>
            </div>
          </el-card>
        </div>
      </div>
    </el-card>
    
    <!-- 上传照片对话框 -->
    <el-dialog v-model="photoDialogVisible" title="上传取件照片" width="400px">
      <el-upload
        class="upload-demo"
        drag
        action="#"
        :auto-upload="false"
        :on-change="handlePhotoChange"
        accept="image/*"
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          将文件拖到此处，或<em>点击上传</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            只能上传jpg/png文件，且不超过2MB
          </div>
        </template>
      </el-upload>
      <template #footer>
        <el-button @click="photoDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitPhoto">确认上传</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useOrderStore } from '../../stores/order.js'
import { useAuthStore } from '../../stores/auth.js'
import { ElMessage } from 'element-plus'
import { UploadFilled } from '@element-plus/icons-vue'

const router = useRouter()
const orderStore = useOrderStore()
const authStore = useAuthStore()

const activeTab = ref('processing')
const photoDialogVisible = ref(false)
const currentOrder = ref(null)
const uploadedPhoto = ref(null)

const courierOrders = computed(() => {
  return orderStore.getOrdersByCourier(authStore.user?.id).sort((a, b) => 
    new Date(b.updateTime) - new Date(a.updateTime)
  )
})

const filteredOrders = computed(() => {
  switch (activeTab.value) {
    case 'processing':
      return courierOrders.value.filter(order => 
        [orderStore.ORDER_STATUS.ACCEPTED, orderStore.ORDER_STATUS.PICKED_UP, 
         orderStore.ORDER_STATUS.DELIVERING, orderStore.ORDER_STATUS.DELIVERED].includes(order.status)
      )
    case 'completed':
      return courierOrders.value.filter(order => order.status === orderStore.ORDER_STATUS.COMPLETED)
    default:
      return courierOrders.value
  }
})

const todayOrders = computed(() => {
  const today = new Date().toDateString()
  return courierOrders.value.filter(order => 
    new Date(order.updateTime).toDateString() === today
  ).length
})

const todayEarnings = computed(() => {
  const today = new Date().toDateString()
  return courierOrders.value
    .filter(order => 
      new Date(order.updateTime).toDateString() === today && 
      order.status === orderStore.ORDER_STATUS.COMPLETED
    )
    .reduce((total, order) => total + order.fee, 0)
})

const formatTime = (time) => {
  return new Date(time).toLocaleString()
}

const hidePhone = (phone) => {
  if (!phone) return ''
  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
}

const getStatusType = (status) => {
  const statusMap = {
    [orderStore.ORDER_STATUS.ACCEPTED]: 'primary',
    [orderStore.ORDER_STATUS.PICKED_UP]: 'primary',
    [orderStore.ORDER_STATUS.DELIVERING]: 'primary',
    [orderStore.ORDER_STATUS.DELIVERED]: 'success',
    [orderStore.ORDER_STATUS.COMPLETED]: 'success'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    [orderStore.ORDER_STATUS.ACCEPTED]: '已接单',
    [orderStore.ORDER_STATUS.PICKED_UP]: '已取件',
    [orderStore.ORDER_STATUS.DELIVERING]: '配送中',
    [orderStore.ORDER_STATUS.DELIVERED]: '已送达',
    [orderStore.ORDER_STATUS.COMPLETED]: '已完成'
  }
  return statusMap[status] || '未知状态'
}

const getStepActive = (status) => {
  const stepMap = {
    [orderStore.ORDER_STATUS.ACCEPTED]: 1,
    [orderStore.ORDER_STATUS.PICKED_UP]: 2,
    [orderStore.ORDER_STATUS.DELIVERING]: 3,
    [orderStore.ORDER_STATUS.DELIVERED]: 4,
    [orderStore.ORDER_STATUS.COMPLETED]: 5
  }
  return stepMap[status] || 0
}

const handlePickupConfirm = async (order) => {
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    orderStore.updateOrderStatus(order.id, orderStore.ORDER_STATUS.PICKED_UP)
    ElMessage.success('取件确认成功')
  } catch (error) {
    ElMessage.error('操作失败，请重试')
  }
}

const handleStartDelivery = async (order) => {
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    orderStore.updateOrderStatus(order.id, orderStore.ORDER_STATUS.DELIVERING)
    ElMessage.success('开始配送')
  } catch (error) {
    ElMessage.error('操作失败，请重试')
  }
}

const handleDeliveryConfirm = async (order) => {
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    orderStore.updateOrderStatus(order.id, orderStore.ORDER_STATUS.DELIVERED)
    ElMessage.success('送达确认成功，等待用户确认收货')
  } catch (error) {
    ElMessage.error('操作失败，请重试')
  }
}

const handleContactUser = (order) => {
  ElMessage.info(`联系用户功能开发中，用户电话：${order.contactPhone}`)
}

const handleUploadPhoto = (order) => {
  currentOrder.value = order
  photoDialogVisible.value = true
}

const handlePhotoChange = (file) => {
  uploadedPhoto.value = file
}

const submitPhoto = () => {
  if (!uploadedPhoto.value) {
    ElMessage.warning('请选择要上传的照片')
    return
  }
  
  // 这里应该上传照片到服务器
  photoDialogVisible.value = false
  ElMessage.success('照片上传成功')
}

const handleViewRating = (order) => {
  ElMessage.info('查看评价功能开发中')
}

onMounted(() => {
  // 可以在这里加载代取员的订单数据
})
</script>

<style scoped>
.my-orders-container {
  width: 100%;
  height: 100%;
  padding: 8px;
  overflow-y: auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: #303133;
}

.stats {
  display: flex;
  gap: 30px;
}

.orders-list {
  margin-top: 20px;
}

.order-card {
  margin-bottom: 20px;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.order-info h4 {
  margin: 0 0 5px 0;
  color: #303133;
}

.order-time {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.order-status {
  text-align: right;
}

.fee-amount {
  font-size: 18px;
  font-weight: bold;
  color: #67c23a;
  margin-top: 5px;
}

.order-content {
  margin: 15px 0;
}

.info-item {
  display: flex;
  margin-bottom: 8px;
}

.label {
  color: #909399;
  width: 80px;
  flex-shrink: 0;
}

.value {
  color: #303133;
  flex: 1;
}

.special-requirements {
  margin-top: 15px;
  padding: 10px;
  background: #fdf6ec;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.order-actions {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.action-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.order-timeline {
  padding: 10px 0;
}

.empty-state {
  text-align: center;
  padding: 40px 0;
}

.upload-demo {
  width: 100%;
}
</style>