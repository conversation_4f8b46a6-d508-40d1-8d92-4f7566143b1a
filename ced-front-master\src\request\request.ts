import axios from 'axios';
const instance = axios.create({
    baseURL: 'http://localhost:18080/api',
    timeout: 15000
});

// 拦截器
instance.interceptors.request.use(config => {
    let token = localStorage.getItem('token')
    if (token) {
        config.headers.Authorization = token;
    }
    return config;
}, err => {
    return Promise.reject(err);
})
instance.interceptors.response.use(result => {
    return result.data;
}, err => {
    return Promise.reject(err);
})
export default instance;