<template>
  <div class="notices-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <div class="header-title">
            <el-icon><Bell /></el-icon>
            <h3>系统公告</h3>
          </div>
          <div class="header-actions">
            <el-button type="primary" size="small" @click="markAllAsRead">
              全部标记已读
            </el-button>
          </div>
        </div>
      </template>
      
      <div class="notices-list">
        <div v-if="notices.length === 0" class="empty-state">
          <el-empty description="暂无公告" :image-size="100" />
        </div>
        
        <div v-else>
          <div 
            v-for="notice in notices" 
            :key="notice.id" 
            class="notice-item"
            :class="{ unread: !notice.read }"
            @click="handleNoticeClick(notice)"
          >
            <div class="notice-header">
              <div class="notice-title">
                <span v-if="!notice.read" class="unread-dot"></span>
                {{ notice.title }}
              </div>
              <div class="notice-meta">
                <el-tag :type="getNoticeType(notice.type)" size="small">
                  {{ getNoticeTypeText(notice.type) }}
                </el-tag>
                <span class="notice-time">{{ formatTime(notice.time) }}</span>
              </div>
            </div>
            <div class="notice-content">
              {{ notice.content }}
            </div>
            <div class="notice-actions">
              <el-button 
                v-if="!notice.read" 
                type="primary" 
                text 
                size="small"
                @click.stop="markAsRead(notice)"
              >
                标记已读
              </el-button>
              <el-button type="info" text size="small">
                查看详情
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Bell } from '@element-plus/icons-vue'

const notices = ref([
  {
    id: 1,
    title: '系统维护通知',
    content: '系统将于今晚22:00-24:00进行维护，期间可能影响正常使用，请提前安排好您的代取订单。',
    type: 'system',
    time: new Date('2024-01-15 10:00'),
    read: false
  },
  {
    id: 2,
    title: '新增快递点',
    content: '新增南校区菜鸟驿站，现在可以选择该取件点了。地址：南校区生活区一楼。',
    type: 'feature',
    time: new Date('2024-01-14 15:30'),
    read: false
  },
  {
    id: 3,
    title: '服务费用调整',
    content: '为了提供更好的服务质量，平台服务费将进行小幅调整，详情请查看费用说明。',
    type: 'important',
    time: new Date('2024-01-13 09:00'),
    read: true
  },
  {
    id: 4,
    title: '春节放假通知',
    content: '春节期间（2月10日-2月17日）代取服务暂停，请同学们提前安排好快递收取。',
    type: 'holiday',
    time: new Date('2024-01-12 14:20'),
    read: true
  },
  {
    id: 5,
    title: '安全提醒',
    content: '请注意保护个人信息安全，不要将取件码泄露给陌生人，如有异常请及时联系客服。',
    type: 'security',
    time: new Date('2024-01-11 16:45'),
    read: true
  }
])

const formatTime = (time) => {
  const now = new Date()
  const diff = now - time
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (days === 0) {
    const hours = Math.floor(diff / (1000 * 60 * 60))
    if (hours === 0) {
      const minutes = Math.floor(diff / (1000 * 60))
      return minutes <= 0 ? '刚刚' : `${minutes}分钟前`
    }
    return `${hours}小时前`
  } else if (days === 1) {
    return '昨天'
  } else if (days < 7) {
    return `${days}天前`
  } else {
    return time.toLocaleDateString()
  }
}

const getNoticeType = (type) => {
  const typeMap = {
    system: 'info',
    feature: 'success',
    important: 'warning',
    holiday: 'primary',
    security: 'danger'
  }
  return typeMap[type] || 'info'
}

const getNoticeTypeText = (type) => {
  const typeMap = {
    system: '系统',
    feature: '功能',
    important: '重要',
    holiday: '节假日',
    security: '安全'
  }
  return typeMap[type] || '通知'
}

const handleNoticeClick = (notice) => {
  if (!notice.read) {
    markAsRead(notice)
  }
}

const markAsRead = (notice) => {
  notice.read = true
  ElMessage.success('已标记为已读')
}

const markAllAsRead = () => {
  notices.value.forEach(notice => {
    notice.read = true
  })
  ElMessage.success('所有公告已标记为已读')
}

onMounted(() => {
  // 可以在这里加载公告数据
})
</script>

<style scoped>
.notices-container {
  width: 100%;
  height: 100%;
  padding: 8px;
  overflow-y: auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-title h3 {
  margin: 0;
  color: #1a1a1a;
  font-size: 18px;
  font-weight: 600;
}

.notices-list {
  max-height: calc(100vh - 200px);
  overflow-y: auto;
}

.notice-item {
  padding: 20px;
  border: 1px solid #f0f0f0;
  border-radius: 12px;
  margin-bottom: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #ffffff;
}

.notice-item:hover {
  border-color: #2563eb;
  box-shadow: 0 2px 8px rgba(37, 99, 235, 0.1);
  transform: translateY(-1px);
}

.notice-item.unread {
  border-left: 4px solid #2563eb;
  background: #f8faff;
}

.notice-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.notice-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
  flex: 1;
}

.unread-dot {
  width: 8px;
  height: 8px;
  background: #2563eb;
  border-radius: 50%;
  flex-shrink: 0;
}

.notice-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-shrink: 0;
}

.notice-time {
  font-size: 12px;
  color: #6b7280;
}

.notice-content {
  color: #4b5563;
  line-height: 1.6;
  margin-bottom: 16px;
  font-size: 14px;
}

.notice-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.empty-state {
  text-align: center;
  padding: 60px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .notice-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .notice-meta {
    width: 100%;
    justify-content: space-between;
  }
  
  .notice-actions {
    justify-content: flex-start;
  }
}
</style>