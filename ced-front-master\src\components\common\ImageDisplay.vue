<template>
  <div class="image-display-container">
    <div 
      class="image-wrapper"
      :class="{ 'loading': loading, 'error': hasError }"
      :style="{ width: width, height: height }"
    >
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-placeholder">
        <el-icon class="loading-icon"><Loading /></el-icon>
        <span>加载中...</span>
      </div>
      
      <!-- 错误状态 -->
      <div v-else-if="hasError" class="error-placeholder">
        <el-icon class="error-icon"><Picture /></el-icon>
        <span>图片加载失败</span>
        <el-button type="text" @click="retry" size="small">重试</el-button>
      </div>
      
      <!-- 空状态 -->
      <div v-else-if="!displayUrl" class="empty-placeholder">
        <el-icon class="empty-icon"><Picture /></el-icon>
        <span>暂无图片</span>
      </div>
      
      <!-- 图片显示 -->
      <div v-else class="image-content">
        <img 
          :src="displayUrl" 
          :alt="alt"
          class="display-image"
          @click="handleImageClick"
          @error="handleImageError"
        />
        
        <!-- 操作按钮 -->
        <div v-if="showActions" class="image-actions">
          <el-button
            type="primary"
            :icon="ZoomIn"
            @click="handlePreview"
            size="small"
            circle
            title="预览"
          />
          <el-button
            v-if="downloadable"
            type="success"
            :icon="Download"
            @click="handleDownload"
            size="small"
            circle
            title="下载"
          />
        </div>
      </div>
    </div>
    
    <!-- 图片预览对话框 -->
    <el-dialog
      v-model="previewVisible"
      title="图片预览"
      width="80%"
      :before-close="closePreview"
      append-to-body
    >
      <div class="preview-container">
        <img :src="displayUrl" :alt="alt" class="preview-image" />
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, watch, defineProps, defineEmits } from 'vue'
import { ElMessage } from 'element-plus'
import { Loading, Picture, ZoomIn, Download } from '@element-plus/icons-vue'
import { useImageHandler } from '../../composables/useImageHandler.js'

const props = defineProps({
  imagePath: {
    type: String,
    default: ''
  },
  alt: {
    type: String,
    default: '图片'
  },
  width: {
    type: String,
    default: '200px'
  },
  height: {
    type: String,
    default: '200px'
  },
  showActions: {
    type: Boolean,
    default: true
  },
  downloadable: {
    type: Boolean,
    default: true
  },
  clickable: {
    type: Boolean,
    default: true
  },
  fit: {
    type: String,
    default: 'cover',
    validator: (value) => ['fill', 'contain', 'cover', 'none', 'scale-down'].includes(value)
  }
})

const emit = defineEmits(['click', 'load-success', 'load-error'])

const {
  loading,
  displayUrl,
  loadImage,
  clearDisplayUrl
} = useImageHandler()

const hasError = ref(false)
const previewVisible = ref(false)

// 监听图片路径变化
watch(() => props.imagePath, async (newPath) => {
  if (newPath) {
    hasError.value = false
    try {
      await loadImage(newPath)
      emit('load-success', newPath)
    } catch (error) {
      hasError.value = true
      emit('load-error', error)
    }
  } else {
    clearDisplayUrl()
    hasError.value = false
  }
}, { immediate: true })

// 重试加载
const retry = async () => {
  if (props.imagePath) {
    hasError.value = false
    try {
      await loadImage(props.imagePath)
      emit('load-success', props.imagePath)
    } catch (error) {
      hasError.value = true
      emit('load-error', error)
    }
  }
}

// 图片点击处理
const handleImageClick = () => {
  if (props.clickable) {
    emit('click', props.imagePath)
    if (props.showActions) {
      handlePreview()
    }
  }
}

// 图片加载错误处理
const handleImageError = () => {
  hasError.value = true
  emit('load-error', new Error('图片加载失败'))
}

// 预览图片
const handlePreview = () => {
  if (displayUrl.value) {
    previewVisible.value = true
  }
}

// 关闭预览
const closePreview = () => {
  previewVisible.value = false
}

// 下载图片
const handleDownload = () => {
  if (displayUrl.value && props.imagePath) {
    try {
      const link = document.createElement('a')
      link.href = displayUrl.value
      link.download = props.imagePath.split('/').pop() || 'image'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      ElMessage.success('图片下载成功')
    } catch (error) {
      console.error('下载失败:', error)
      ElMessage.error('图片下载失败')
    }
  }
}
</script>

<style scoped>
.image-display-container {
  position: relative;
  display: inline-block;
}

.image-wrapper {
  position: relative;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
  background: #f5f7fa;
  transition: all 0.3s ease;
}

.image-wrapper:hover {
  border-color: #c0c4cc;
}

.loading-placeholder,
.error-placeholder,
.empty-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #909399;
  font-size: 14px;
}

.loading-icon,
.error-icon,
.empty-icon {
  font-size: 32px;
  margin-bottom: 8px;
}

.loading-icon {
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.error-icon {
  color: #f56c6c;
}

.empty-icon {
  color: #c0c4cc;
}

.image-content {
  position: relative;
  width: 100%;
  height: 100%;
}

.display-image {
  width: 100%;
  height: 100%;
  object-fit: v-bind(fit);
  cursor: v-bind('clickable ? "pointer" : "default"');
  transition: transform 0.3s ease;
}

.display-image:hover {
  transform: v-bind('clickable ? "scale(1.05)" : "none"');
}

.image-actions {
  position: absolute;
  top: 8px;
  right: 8px;
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.image-content:hover .image-actions {
  opacity: 1;
}

.preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.preview-image {
  max-width: 100%;
  max-height: 70vh;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .image-actions {
    opacity: 1;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 4px;
    padding: 4px;
  }
  
  .preview-image {
    max-height: 60vh;
  }
}
</style>