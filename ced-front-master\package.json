{"name": "my-vue-demo", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.8.4", "element-plus": "^2.9.8", "pinia": "^3.0.2", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@types/node": "^22.14.1", "@vitejs/plugin-vue": "^5.2.2", "stylus": "^0.64.0", "stylus-loader": "^8.1.1", "typescript": "^5.8.3", "vite": "^6.3.1"}}