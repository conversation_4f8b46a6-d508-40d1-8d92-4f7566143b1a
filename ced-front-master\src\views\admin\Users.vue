<template>
  <div class="users-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <h3>用户管理</h3>
          <div class="header-actions">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索用户"
              style="width: 200px; margin-right: 10px;"
              clearable
            />
            <el-button type="primary" @click="searchUsers">搜索</el-button>
          </div>
        </div>
      </template>
      
      <div class="filter-bar">
        <el-row :gutter="20">
          <el-col :span="4">
            <el-select v-model="filters.campus" placeholder="选择校区" clearable>
              <el-option label="本部校区" value="main" />
              <el-option label="南校区" value="south" />
              <el-option label="北校区" value="north" />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-select v-model="filters.status" placeholder="用户状态" clearable>
              <el-option label="正常" value="active" />
              <el-option label="禁用" value="disabled" />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-select v-model="filters.userType" placeholder="用户类型" clearable>
              <el-option label="普通用户" :value="1" />
              <el-option label="代取员" :value="2" />
              <el-option label="管理员" :value="3" />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-date-picker
              v-model="filters.registerDate"
              type="daterange"
              range-separator="至"
              start-placeholder="注册开始日期"
              end-placeholder="注册结束日期"
              size="default"
            />
          </el-col>
          <el-col :span="6">
            <el-button @click="applyFilters">筛选</el-button>
            <el-button @click="clearFilters">清空</el-button>
          </el-col>
        </el-row>
      </div>
      
      <el-table :data="filteredUsers" style="width: 100%" v-loading="loading">
        <el-table-column prop="userId" label="用户ID" width="100" />
        <el-table-column prop="realName" label="姓名" width="120" />
        <el-table-column prop="studentId" label="学号" width="120" />
        <el-table-column prop="phone" label="手机号" width="130" />
        <el-table-column prop="campus" label="校区" width="100">
          <template #default="scope">
            {{ getCampusName(scope.row.campus) }}
          </template>
        </el-table-column>
        <el-table-column prop="dormInfo" label="宿舍信息" width="150">
          <template #default="scope">
            {{ scope.row.dormBuilding || '未设置' }} {{ scope.row.roomNumber || '' }}
          </template>
        </el-table-column>
        <el-table-column prop="orderCount" label="订单数" width="80" />
        <el-table-column prop="totalSpent" label="消费金额" width="100">
          <template #default="scope">
            ¥{{ scope.row.totalSpent || '0.00' }}
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="注册时间" width="180">
          <template #default="scope">
            {{ formatTime(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="80">
          <template #default="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
              {{ scope.row.statusText }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="280" fixed="right">
          <template #default="scope">
            <el-button type="primary" size="small" @click="viewUserDetail(scope.row)">
              详情
            </el-button>
            <el-button 
              :type="scope.row.status === 1 ? 'danger' : 'success'" 
              size="small" 
              @click="toggleUserStatus(scope.row)"
            >
              {{ scope.row.status === 1 ? '禁用' : '启用' }}
            </el-button>
            <el-button type="warning" size="small" @click="resetPassword(scope.row)">
              重置密码
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="totalUsers"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    
    <!-- 用户详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="用户详情" width="600px">
      <div v-if="selectedUser" class="user-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="用户ID">{{ selectedUser.userId }}</el-descriptions-item>
          <el-descriptions-item label="姓名">{{ selectedUser.realName }}</el-descriptions-item>
          <el-descriptions-item label="学号">{{ selectedUser.studentId || '未设置' }}</el-descriptions-item>
          <el-descriptions-item label="手机号">{{ selectedUser.phone }}</el-descriptions-item>
          <el-descriptions-item label="校区">{{ getCampusName(selectedUser.campus) }}</el-descriptions-item>
          <el-descriptions-item label="宿舍">{{ selectedUser.dormBuilding || '未设置' }} {{ selectedUser.roomNumber || '' }}</el-descriptions-item>
          <el-descriptions-item label="注册时间">{{ formatTime(selectedUser.createTime) }}</el-descriptions-item>
          <el-descriptions-item label="最后登录">{{ formatTime(selectedUser.updateTime) }}</el-descriptions-item>
          <el-descriptions-item label="订单数量">{{ selectedUser.orderCount || 0 }}</el-descriptions-item>
          <el-descriptions-item label="消费金额">¥{{ selectedUser.totalSpent || '0.00' }}</el-descriptions-item>
          <el-descriptions-item label="用户类型">{{ selectedUser.userTypeText }}</el-descriptions-item>
          <el-descriptions-item label="账户状态">
            <el-tag :type="selectedUser.status === 1 ? 'success' : 'danger'">
              {{ selectedUser.statusText }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
        
        <div class="user-orders" style="margin-top: 20px;">
          <h4>最近订单</h4>
          <el-table :data="selectedUser.recentOrders" style="width: 100%">
            <el-table-column prop="id" label="订单号" width="120" />
            <el-table-column prop="expressPoint" label="快递点" />
            <el-table-column prop="fee" label="费用">
              <template #default="scope">
                ¥{{ scope.row.fee }}
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态">
              <template #default="scope">
                <el-tag :type="getOrderStatusType(scope.row.status)">
                  {{ getOrderStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间">
              <template #default="scope">
                {{ formatTime(scope.row.createTime) }}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useOrderStore } from '../../stores/order.js'
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'
import { adminApi } from '../../request/adminApi.js'

const orderStore = useOrderStore()

const loading = ref(false)
const searchKeyword = ref('')
const detailDialogVisible = ref(false)
const selectedUser = ref(null)
const currentPage = ref(1)
const pageSize = ref(20)

const filters = reactive({
  campus: '',
  status: '',
  userType: '',
  registerDate: []
})

// 用户数据
const users = ref([])
const totalCount = ref(0)

// 过滤后的用户列表
const filteredUsers = computed(() => {
  return users.value
})

// 总用户数
const totalUsers = computed(() => totalCount.value)

const getCampusName = (campus) => {
  const campusMap = {
    'main': '本部校区',
    'south': '南校区',
    'north': '北校区'
  }
  return campusMap[campus] || campus
}

const formatTime = (time) => {
  return new Date(time).toLocaleString()
}

const getOrderStatusType = (status) => {
  const statusMap = {
    [orderStore.ORDER_STATUS.PENDING]: 'warning',
    [orderStore.ORDER_STATUS.ACCEPTED]: 'primary',
    [orderStore.ORDER_STATUS.COMPLETED]: 'success',
    [orderStore.ORDER_STATUS.CANCELLED]: 'danger'
  }
  return statusMap[status] || 'info'
}

const getOrderStatusText = (status) => {
  const statusMap = {
    [orderStore.ORDER_STATUS.PENDING]: '待接单',
    [orderStore.ORDER_STATUS.ACCEPTED]: '已接单',
    [orderStore.ORDER_STATUS.COMPLETED]: '已完成',
    [orderStore.ORDER_STATUS.CANCELLED]: '已取消'
  }
  return statusMap[status] || '未知状态'
}

// 搜索用户
const searchUsers = () => {
  currentPage.value = 1
  fetchUserList()
}

// 应用筛选条件
const applyFilters = () => {
  currentPage.value = 1
  fetchUserList()
}

// 清空筛选条件
const clearFilters = () => {
  filters.campus = ''
  filters.status = ''
  filters.registerDate = []
  ElMessage.info('筛选条件已清空')
  currentPage.value = 1
  fetchUserList()
}

const viewUserDetail = async (user) => {
  const loadingInstance = ElLoading.service({ text: '加载用户详情...' })
  
  try {
    const userDetail = await getUserDetail(user.userId)
    
    if (userDetail) {
      selectedUser.value = userDetail
      detailDialogVisible.value = true
    }
  } catch (error) {
    console.error('获取用户详情失败:', error)
    ElMessage.error('获取用户详情失败，请重试')
  } finally {
    loadingInstance.close()
  }
}

// 切换用户状态（启用/禁用）
const toggleUserStatus = async (user) => {
  const newStatus = user.status === 1 ? 0 : 1
  const action = user.status === 1 ? '禁用' : '启用'
  
  try {
    await ElMessageBox.confirm(`确定要${action}用户 ${user.realName || user.username} 吗？`, '确认操作', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const loadingInstance = ElLoading.service({ text: `正在${action}用户...` })
    
    try {
      const response = await adminApi.updateUserStatus(user.userId, newStatus)
      
      loadingInstance.close()
      
      if (response.code === 200) {
        user.status = newStatus
        user.statusText = newStatus === 1 ? '正常' : '禁用'
        ElMessage.success(`用户${action}成功`)
      } else {
        ElMessage.error(response.msg || `用户${action}失败`)
      }
    } catch (error) {
      loadingInstance.close()
      console.error(`${action}用户失败:`, error)
      ElMessage.error(`${action}用户失败，请重试`)
    }
  } catch {
    // 用户取消操作
  }
}

// 重置用户密码
const resetPassword = async (user) => {
  try {
    await ElMessageBox.confirm(`确定要重置用户 ${user.realName || user.username} 的密码吗？`, '重置密码', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const loadingInstance = ElLoading.service({ text: '正在重置密码...' })
    
    try {
      const response = await adminApi.resetUserPassword(user.userId)
      
      loadingInstance.close()
      
      if (response.code === 200) {
        ElMessage.success('密码重置成功，新密码已发送到用户手机')
      } else {
        ElMessage.error(response.msg || '密码重置失败')
      }
    } catch (error) {
      loadingInstance.close()
      console.error('重置密码失败:', error)
      ElMessage.error('重置密码失败，请重试')
    }
  } catch {
    // 用户取消操作
  }
}

const handleSizeChange = (size) => {
  pageSize.value = size
  fetchUserList()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  fetchUserList()
}

// 获取用户列表
const fetchUserList = async () => {
  loading.value = true
  
  try {
    // 构建查询参数
    const params = {
      current: currentPage.value,  // 修改为 current，符合后端分页参数
      size: pageSize.value,
      keyword: searchKeyword.value || undefined
    }
    
    // 添加校区筛选
    if (filters.campus) {
      params.campus = filters.campus
    }
    
    // 添加状态筛选
    if (filters.status) {
      params.status = filters.status === 'active' ? 1 : 0
    }
    
    // 添加用户类型筛选
    if (filters.userType !== '') {
      params.userType = Number(filters.userType)
    }
    
    // 添加日期筛选
    if (filters.registerDate && filters.registerDate.length === 2) {
      params.startDate = filters.registerDate[0].toISOString().split('T')[0]
      params.endDate = filters.registerDate[1].toISOString().split('T')[0]
    }
    
    const response = await adminApi.getAllUsers(params)
    
    if (response.code === 200) {
      // 处理用户数据，添加状态文本等
      users.value = response.data.records.map(user => ({
        ...user,
        statusText: user.status === 1 ? '正常' : '禁用',
        userTypeText: getUserTypeText(user.userType)
      }))
      totalCount.value = response.data.total
    } else {
      ElMessage.error(response.msg || '获取用户列表失败')
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败，请重试')
  } finally {
    loading.value = false
  }
}

// 获取用户类型文本
const getUserTypeText = (type) => {
  const typeMap = {
    1: '普通用户',
    2: '代取员',
    3: '管理员'
  }
  return typeMap[type] || '未知'
}

// 获取用户详情
const getUserDetail = async (userId) => {
  try {
    const response = await adminApi.getUserDetail(userId)
    
    if (response.code === 200) {
      // 处理用户详情数据
      return {
        ...response.data,
        statusText: response.data.status === 1 ? '正常' : '禁用',
        userTypeText: getUserTypeText(response.data.userType),
        recentOrders: response.data.recentOrders || []
      }
    } else {
      ElMessage.error(response.msg || '获取用户详情失败')
      return null
    }
  } catch (error) {
    console.error('获取用户详情失败:', error)
    ElMessage.error('获取用户详情失败，请重试')
    return null
  }
}

onMounted(() => {
  // 初始化数据
  fetchUserList()
})
</script>

<style scoped>
.users-container {
  width: 100%;
  height: 100%;
  padding: 8px;
  overflow-y: auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: #303133;
}

.header-actions {
  display: flex;
  align-items: center;
}

.filter-bar {
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.user-detail {
  max-height: 500px;
  overflow-y: auto;
}

.user-orders h4 {
  margin-bottom: 15px;
  color: #303133;
}
</style>