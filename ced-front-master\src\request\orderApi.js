import request from './request'

// 创建订单
export const createOrderApi = (data) => {
  return request.post('/order/create', data)
}

// 接单
export const acceptOrderApi = (orderId) => {
  return request.post(`/order/accept/${orderId}`)
}

// 取消订单
export const cancelOrderApi = (orderId, reason) => {
  return request.post(`/order/cancel/${orderId}?reason=${encodeURIComponent(reason)}`)
}

// 确认取件
export const confirmPickupApi = (orderId) => {
  return request.post(`/order/confirm-pickup/${orderId}`)
}

// 确认送达
export const confirmDeliveryApi = (orderId) => {
  return request.post(`/order/confirm-delivery/${orderId}`)
}

// 确认收货
export const confirmReceiveApi = (orderId) => {
  return request.post(`/order/confirm-receive/${orderId}`)
}

// 查询我的订单（用户端）
export const getMyOrdersApi = () => {
  return request.get('/order/my-orders')
}

// 查询我接的订单（代取员端）
export const getPickupOrdersApi = () => {
  return request.get('/order/pickup-orders')
}

// 查询待接单列表
export const getPendingOrdersApi = () => {
  return request.get('/order/pending')
}

// 查询订单详情
export const getOrderDetailApi = (orderId) => {
  return request.get(`/order/${orderId}`)
}