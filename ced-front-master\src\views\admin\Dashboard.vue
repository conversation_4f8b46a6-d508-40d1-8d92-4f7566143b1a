<template>
  <div class="dashboard-container">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ stats.totalUsers }}</div>
            <div class="stat-label">总用户数</div>
          </div>
          <el-icon class="stat-icon" color="#409eff"><User /></el-icon>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ stats.totalCouriers }}</div>
            <div class="stat-label">代取员数</div>
          </div>
          <el-icon class="stat-icon" color="#67c23a"><UserFilled /></el-icon>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ stats.totalOrders }}</div>
            <div class="stat-label">总订单数</div>
          </div>
          <el-icon class="stat-icon" color="#e6a23c"><List /></el-icon>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">¥{{ stats.totalRevenue }}</div>
            <div class="stat-label">平台收入</div>
          </div>
          <el-icon class="stat-icon" color="#f56c6c"><Money /></el-icon>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- 订单趋势图 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>订单趋势</span>
              <el-select v-model="chartPeriod" size="small" style="width: 100px;">
                <el-option label="7天" value="7d" />
                <el-option label="30天" value="30d" />
                <el-option label="90天" value="90d" />
              </el-select>
            </div>
          </template>
          <div class="chart-container">
            <div class="chart-placeholder">
              <el-icon size="60" color="#ddd"><DataLine /></el-icon>
              <p>订单趋势图表</p>
              <p class="chart-note">（此处应集成图表库如ECharts）</p>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <!-- 快递点统计 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>热门快递点</span>
            </div>
          </template>
          <div class="express-points">
            <div v-for="point in popularExpressPoints" :key="point.name" class="point-item">
              <div class="point-info">
                <div class="point-name">{{ point.name }}</div>
                <div class="point-location">{{ point.location }}</div>
              </div>
              <div class="point-stats">
                <div class="point-orders">{{ point.orders }}单</div>
                <el-progress :percentage="point.percentage" :show-text="false" />
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- 待处理事项 -->
      <el-col :span="8">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>待处理事项</span>
              <el-badge :value="pendingTasks.length" class="item">
                <el-icon><Bell /></el-icon>
              </el-badge>
            </div>
          </template>
          <div class="pending-tasks">
            <div v-if="pendingTasks.length === 0" class="empty-tasks">
              <el-icon size="40" color="#ddd"><Check /></el-icon>
              <p>暂无待处理事项</p>
            </div>
            <div v-else>
              <div v-for="task in pendingTasks" :key="task.id" class="task-item">
                <div class="task-content">
                  <div class="task-title">{{ task.title }}</div>
                  <div class="task-time">{{ task.time }}</div>
                </div>
                <el-button type="primary" size="small" @click="handleTask(task)">
                  处理
                </el-button>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <!-- 系统状态 -->
      <el-col :span="8">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>系统状态</span>
            </div>
          </template>
          <div class="system-status">
            <div class="status-item">
              <div class="status-label">系统运行状态</div>
              <el-tag type="success">正常</el-tag>
            </div>
            <div class="status-item">
              <div class="status-label">在线用户数</div>
              <span class="status-value">{{ onlineUsers }}</span>
            </div>
            <div class="status-item">
              <div class="status-label">活跃代取员</div>
              <span class="status-value">{{ activeCouriers }}</span>
            </div>
            <div class="status-item">
              <div class="status-label">服务器负载</div>
              <el-progress :percentage="serverLoad" :color="getLoadColor(serverLoad)" />
            </div>
          </div>
        </el-card>
      </el-col>
      
      <!-- 最新订单 -->
      <el-col :span="8">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>最新订单</span>
              <el-link type="primary" @click="$router.push('/admin/orders')">查看全部</el-link>
            </div>
          </template>
          <div class="recent-orders">
            <div v-if="recentOrders.length === 0" class="empty-orders">
              <el-icon size="40" color="#ddd"><Document /></el-icon>
              <p>暂无最新订单</p>
            </div>
            <div v-else>
              <div v-for="order in recentOrders" :key="order.id" class="order-item">
                <div class="order-info">
                  <div class="order-title">{{ order.expressPointName }}</div>
                  <div class="order-user">用户：{{ order.userName }}</div>
                </div>
                <div class="order-status">
                  <el-tag :type="getStatusType(order.status)" size="small">
                    {{ getStatusText(order.status) }}
                  </el-tag>
                  <div class="order-fee">¥{{ order.fee }}</div>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useOrderStore } from '../../stores/order.js'
import { ElMessage, ElLoading } from 'element-plus'
import { User, UserFilled, List, Money, DataLine, Bell, Check, Document } from '@element-plus/icons-vue'
import { adminApi } from '../../request/adminApi.js'

const router = useRouter()
const orderStore = useOrderStore()

const loading = ref(false)
const chartPeriod = ref('7d')
const onlineUsers = ref(0)
const activeCouriers = ref(0)
const serverLoad = ref(0)

const stats = ref({
  totalUsers: 0,
  totalCouriers: 0,
  totalOrders: 0,
  totalRevenue: 0
})

const popularExpressPoints = ref([])
const pendingTasks = ref([])
const recentOrders = ref([])

const getStatusType = (status) => {
  const statusMap = {
    [orderStore.ORDER_STATUS.PENDING]: 'warning',
    [orderStore.ORDER_STATUS.ACCEPTED]: 'primary',
    [orderStore.ORDER_STATUS.PICKED_UP]: 'primary',
    [orderStore.ORDER_STATUS.DELIVERING]: 'primary',
    [orderStore.ORDER_STATUS.DELIVERED]: 'success',
    [orderStore.ORDER_STATUS.COMPLETED]: 'success',
    [orderStore.ORDER_STATUS.CANCELLED]: 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    [orderStore.ORDER_STATUS.PENDING]: '待接单',
    [orderStore.ORDER_STATUS.ACCEPTED]: '已接单',
    [orderStore.ORDER_STATUS.PICKED_UP]: '已取件',
    [orderStore.ORDER_STATUS.DELIVERING]: '配送中',
    [orderStore.ORDER_STATUS.DELIVERED]: '已送达',
    [orderStore.ORDER_STATUS.COMPLETED]: '已完成',
    [orderStore.ORDER_STATUS.CANCELLED]: '已取消'
  }
  return statusMap[status] || '未知状态'
}

const getLoadColor = (load) => {
  if (load < 50) return '#67c23a'
  if (load < 80) return '#e6a23c'
  return '#f56c6c'
}

const handleTask = (task) => {
  switch (task.type) {
    case 'courier_review':
      router.push('/admin/couriers')
      break
    case 'complaint':
      ElMessage.info('投诉处理功能开发中')
      break
    case 'order_issue':
      router.push('/admin/orders')
      break
    default:
      ElMessage.info('功能开发中')
  }
}

// 获取统计数据
const fetchStatistics = async () => {
  try {
    const response = await adminApi.getStatistics()
    if (response.code === 200) {
      stats.value = response.data
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

// 获取订单趋势数据
const fetchOrderTrend = async () => {
  try {
    const response = await adminApi.getOrderTrend(chartPeriod.value)
    if (response.code === 200) {
      // 这里应该处理图表数据
      console.log('订单趋势数据:', response.data)
    }
  } catch (error) {
    console.error('获取订单趋势数据失败:', error)
  }
}

// 获取热门快递点数据
const fetchPopularExpressPoints = async () => {
  try {
    const response = await adminApi.getPopularExpressPoints()
    if (response.code === 200) {
      popularExpressPoints.value = response.data
    }
  } catch (error) {
    console.error('获取热门快递点数据失败:', error)
  }
}

// 获取待处理事项
const fetchPendingTasks = async () => {
  try {
    const response = await adminApi.getPendingTasks()
    if (response.code === 200) {
      pendingTasks.value = response.data
    }
  } catch (error) {
    console.error('获取待处理事项失败:', error)
  }
}

// 获取系统状态
const fetchSystemStatus = async () => {
  try {
    const response = await adminApi.getSystemStatus()
    if (response.code === 200) {
      onlineUsers.value = response.data.onlineUsers
      activeCouriers.value = response.data.activeCouriers
      serverLoad.value = response.data.serverLoad
    }
  } catch (error) {
    console.error('获取系统状态失败:', error)
  }
}

// 获取最新订单
const fetchRecentOrders = async () => {
  try {
    const response = await adminApi.getRecentOrders()
    if (response.code === 200) {
      recentOrders.value = response.data
    }
  } catch (error) {
    console.error('获取最新订单失败:', error)
  }
}

// 加载所有数据
const loadAllData = async () => {
  loading.value = true
  const loadingInstance = ElLoading.service({ target: '.dashboard-container', text: '加载数据中...' })
  
  try {
    await Promise.all([
      fetchStatistics(),
      fetchOrderTrend(),
      fetchPopularExpressPoints(),
      fetchPendingTasks(),
      fetchSystemStatus(),
      fetchRecentOrders()
    ])
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败，请刷新页面重试')
  } finally {
    loading.value = false
    loadingInstance.close()
  }
}

// 监听图表周期变化
watch(chartPeriod, () => {
  fetchOrderTrend()
})

onMounted(() => {
  loadAllData()
})
</script>

<style scoped>
.dashboard-container {
  width: 100%;
  height: 100%;
  padding: 8px;
  overflow-y: auto;
}

.stats-row {
  margin-bottom: 20px;
}

.stat-card {
  position: relative;
  overflow: hidden;
}

.stat-content {
  padding: 10px 0;
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.stat-icon {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 40px;
  opacity: 0.3;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-container {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  text-align: center;
  color: #909399;
}

.chart-note {
  font-size: 12px;
  margin-top: 10px;
}

.express-points {
  max-height: 300px;
  overflow-y: auto;
}

.point-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #f0f0f0;
}

.point-item:last-child {
  border-bottom: none;
}

.point-info {
  flex: 1;
}

.point-name {
  font-weight: 500;
  margin-bottom: 5px;
}

.point-location {
  font-size: 12px;
  color: #909399;
}

.point-stats {
  text-align: right;
  width: 120px;
}

.point-orders {
  font-size: 14px;
  color: #303133;
  margin-bottom: 5px;
}

.pending-tasks {
  max-height: 300px;
  overflow-y: auto;
}

.empty-tasks, .empty-orders {
  text-align: center;
  padding: 40px 0;
  color: #909399;
}

.task-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #f0f0f0;
}

.task-item:last-child {
  border-bottom: none;
}

.task-content {
  flex: 1;
}

.task-title {
  font-weight: 500;
  margin-bottom: 5px;
}

.task-time {
  font-size: 12px;
  color: #909399;
}

.system-status {
  padding: 10px 0;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
}

.status-label {
  color: #606266;
}

.status-value {
  font-weight: 500;
  color: #303133;
}

.recent-orders {
  max-height: 300px;
  overflow-y: auto;
}

.order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #f0f0f0;
}

.order-item:last-child {
  border-bottom: none;
}

.order-info {
  flex: 1;
}

.order-title {
  font-weight: 500;
  margin-bottom: 5px;
}

.order-user {
  font-size: 12px;
  color: #909399;
}

.order-status {
  text-align: right;
}

.order-fee {
  font-size: 14px;
  font-weight: 500;
  color: #67c23a;
  margin-top: 5px;
}
</style>