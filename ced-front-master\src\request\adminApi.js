import request from './request'

// 管理员相关API
export const adminApi = {
  // 获取系统统计数据
  getStatistics() {
    return request.get('/admin/statistics')
  },
  
  // 获取订单趋势数据
  getOrderTrend(period) {
    return request.get('/admin/statistics/order-trend', {
      params: { period }
    })
  },
  
  // 获取热门快递点数据
  getPopularExpressPoints() {
    return request.get('/admin/statistics/popular-express-points')
  },
  
  // 获取待处理事项
  getPendingTasks() {
    return request.get('/admin/statistics/pending-tasks')
  },
  
  // 获取系统状态
  getSystemStatus() {
    return request.get('/admin/statistics/system-status')
  },
  
  // 获取最新订单
  getRecentOrders() {
    return request.get('/admin/statistics/recent-orders')
  },
  
  // 获取所有用户列表
  getAllUsers(params) {
    return request.get('/admin/users', {
      params
    })
  },
  
  // 更新用户状态（禁用/启用）
  updateUserStatus(userId, status) {
    return request.post(`/admin/users/${userId}/status`, null, {
      params: { status }
    })
  },
  
  // 获取用户详情
  getUserDetail(userId) {
    return request.get(`/admin/users/${userId}/detail`)
  },
  
  // 重置用户密码
  resetUserPassword(userId) {
    return request.post(`/admin/users/${userId}/reset-password`)
  },
  
  // ===== 代取员管理接口 =====
  
  // 获取代取员列表
  getCouriers(params) {
    return request.get('/admin/couriers', {
      params
    })
  },
  
  // 获取待审核的代取员列表
  getPendingCouriers() {
    return request.get('/admin/couriers/pending')
  },
  
  // 获取已通过审核的代取员列表
  getApprovedCouriers() {
    return request.get('/admin/couriers/approved')
  },
  
  // 获取已拒绝的代取员列表
  getRejectedCouriers() {
    return request.get('/admin/couriers/rejected')
  },
  
  // 获取代取员详情
  getCourierDetail(courierId) {
    return request.get(`/admin/couriers/${courierId}/detail`)
  },
  
  // 审核代取员
  auditCourier(courierId, status, comment) {
    return request.post(`/admin/couriers/${courierId}/audit`, {
      status,
      comment
    })
  },
  
  // 更新代取员状态（启用/禁用）
  updateCourierStatus(courierId, status) {
    return request.post(`/admin/couriers/${courierId}/status`, null, {
      params: { status }
    })
  },
  
  // ===== 订单管理接口 =====
  
  // 获取订单列表
  getOrders(params) {
    return request.get('/admin/orders', {
      params
    })
  },
  
  // 获取订单详情
  getOrderDetail(orderId) {
    return request.get(`/admin/orders/${orderId}/detail`)
  },
  
  // 分配代取员
  assignCourier(orderId, courierId, note) {
    return request.post(`/admin/orders/${orderId}/assign`, {
      courierId,
      note
    })
  },
  
  // 取消订单
  cancelOrder(orderId, reason) {
    return request.post(`/admin/orders/${orderId}/cancel`, {
      reason
    })
  },
  
  // 删除订单
  deleteOrder(orderId) {
    return request.delete(`/admin/orders/${orderId}`)
  },
  
  // 批量删除订单
  batchDeleteOrders(orderIds) {
    return request.post('/admin/orders/batch-delete', {
      orderIds
    })
  },
  
  // 导出订单数据
  exportOrders(params) {
    return request.get('/admin/orders/export', {
      params,
      responseType: 'blob'
    })
  },
  
  // ===== 系统公告接口 =====
  
  // 获取公告列表
  getNotices() {
    return request.get('/admin/notices')
  },
  
  // 创建公告
  createNotice(noticeData) {
    return request.post('/admin/notices', noticeData)
  },
  
  // 更新公告
  updateNotice(noticeId, noticeData) {
    return request.put(`/admin/notices/${noticeId}`, noticeData)
  },
  
  // 删除公告
  deleteNotice(noticeId) {
    return request.delete(`/admin/notices/${noticeId}`)
  },
  
  // 发布公告
  publishNotice(noticeId) {
    return request.post(`/admin/notices/${noticeId}/publish`)
  },
  
  // 撤回公告
  unpublishNotice(noticeId) {
    return request.post(`/admin/notices/${noticeId}/unpublish`)
  },
  
  // ===== 快递站点接口 =====
  
  // 添加快递站点
  addStation(stationData) {
    return request.post('/admin/stations', stationData)
  },
  
  // 更新快递站点
  updateStation(stationId, stationData) {
    return request.put(`/admin/stations/${stationId}`, stationData)
  },
  
  // 删除快递站点
  deleteStation(stationId) {
    return request.delete(`/admin/stations/${stationId}`)
  }
}
