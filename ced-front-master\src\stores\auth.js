import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useAuthStore = defineStore('auth', () => {
  const user = ref(null)
  const token = ref(localStorage.getItem('token') || '')
  const userType = ref(localStorage.getItem('userType') || '') // 'student', 'courier', 'admin'

  const login = (userData, userToken) => {
    user.value = userData
    token.value = userToken
    
    // 根据用户类型设置userType
    let type = 'student'
    if (userData.userType === 2) {
      type = 'courier'
    } else if (userData.userType === 3) {
      type = 'admin'
    }
    
    userType.value = type
    localStorage.setItem('token', userToken)
    localStorage.setItem('userType', type)
    localStorage.setItem('user', JSON.stringify(userData))
  }

  const logout = () => {
    user.value = null
    token.value = ''
    userType.value = ''
    localStorage.removeItem('token')
    localStorage.removeItem('userType')
    localStorage.removeItem('user')
  }

  const initAuth = () => {
    const savedUser = localStorage.getItem('user')
    if (savedUser && token.value) {
      try {
        user.value = JSON.parse(savedUser)
        // 确保头像URL是完整的
        if (user.value && user.value.avatar && !user.value.avatar.startsWith('http') && !user.value.avatar.startsWith('/')) {
          // 如果头像路径不是完整URL，添加基础路径
          user.value.avatar = import.meta.env.VITE_API_BASE_URL + user.value.avatar
        }
      } catch (error) {
        console.error('解析用户信息失败:', error)
        logout() // 解析失败时登出
      }
    }
  }

  const updateUserInfo = (newUserInfo) => {
    if (user.value) {
      // 处理头像URL
      if (newUserInfo.avatar && !newUserInfo.avatar.startsWith('http') && !newUserInfo.avatar.startsWith('/')) {
        newUserInfo.avatar = import.meta.env.VITE_API_BASE_URL + newUserInfo.avatar
      }
      
      // 更新用户信息
      Object.assign(user.value, newUserInfo)
      localStorage.setItem('user', JSON.stringify(user.value))
    }
  }

  const getUserTypeText = () => {
    switch (userType.value) {
      case 'student': return '学生'
      case 'courier': return '代取员'
      case 'admin': return '管理员'
      default: return '未知'
    }
  }

  return {
    user,
    token,
    userType,
    login,
    logout,
    initAuth,
    updateUserInfo,
    getUserTypeText
  }
})