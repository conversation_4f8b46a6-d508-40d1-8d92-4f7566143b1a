// API响应数据类型定义

export interface ReportData<T = any> {
  code: number;
  msg: string;
  data: T;
}

export interface PaginationData<T = any> {
  records: T[];
  total: number;
  size: number;
  current: number;
  pages: number;
}

export interface FileUploadData {
  path: string;
  url: string;
  name: string;
  size: number;
}

export interface UserInfo {
  userId: number;
  username: string;
  realName: string;
  phone: string;
  email: string;
  studentId: string;
  avatar: string;
  userType: number;
  status: number;
  auditStatus: number;
}

export interface OrderInfo {
  orderId: number;
  orderNo: string;
  userId: number;
  pickupUserId: number;
  stationId: number;
  pickupCode: string;
  trackingNumber: string;
  expressDescription: string;
  addressId: number;
  deliveryAddress: string;
  contactName: string;
  contactPhone: string;
  expectedTime: string;
  pickupTime: string;
  deliveryTime: string;
  serviceFee: number;
  deposit: number;
  totalAmount: number;
  orderStatus: number;
  payStatus: number;
  remark: string;
  cancelReason: string;
  createTime: string;
  updateTime: string;
}

export interface AddressInfo {
  addressId: number;
  userId: number;
  campus: string;
  building: string;
  room: string;
  detailAddress: string;
  contactName: string;
  contactPhone: string;
  isDefault: number;
  createTime: string;
  updateTime: string;
}

export interface ExpressStationInfo {
  stationId: number;
  stationName: string;
  stationType: string;
  campus: string;
  location: string;
  openTime: string;
  contactPhone: string;
  status: number;
  createTime: string;
  updateTime: string;
}