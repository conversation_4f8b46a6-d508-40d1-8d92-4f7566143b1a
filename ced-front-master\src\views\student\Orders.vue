<template>
  <div class="orders-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <h3>我的订单</h3>
          <el-button type="primary" @click="$router.push('/student/create-order')">
            <el-icon><Plus /></el-icon>
            发布新订单
          </el-button>
        </div>
      </template>
      
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <el-tab-pane label="全部订单" name="all" />
        <el-tab-pane label="待接单" name="pending" />
        <el-tab-pane label="进行中" name="processing" />
        <el-tab-pane label="已完成" name="completed" />
        <el-tab-pane label="已取消" name="cancelled" />
      </el-tabs>
      
      <div class="orders-list">
        <div v-if="filteredOrders.length === 0" class="empty-state">
          <el-empty description="暂无订单" :image-size="100">
            <el-button type="primary" @click="$router.push('/student/create-order')">
              发布第一个订单
            </el-button>
          </el-empty>
        </div>
        
        <div v-else>
          <el-card v-for="order in filteredOrders" :key="order.id" class="order-card" shadow="hover">
            <div class="order-header">
              <div class="order-info">
                <h4>{{ order.expressPointName }}</h4>
                <p class="order-time">{{ formatTime(order.createTime) }}</p>
              </div>
              <el-tag :type="getStatusType(order.status)" size="large">
                {{ getStatusText(order.status) }}
              </el-tag>
            </div>
            
            <el-divider />
            
            <div class="order-content">
              <el-row :gutter="20">
                <el-col :span="12">
                  <div class="info-item">
                    <span class="label">取件码：</span>
                    <span class="value">{{ order.pickupCode }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">送达地址：</span>
                    <span class="value">{{ order.deliveryAddress }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">期望送达：</span>
                    <span class="value">{{ formatTime(order.expectedTime) }}</span>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="info-item">
                    <span class="label">代取费用：</span>
                    <span class="value fee">¥{{ order.fee }}</span>
                  </div>
                  <div class="info-item" v-if="order.courierId">
                    <span class="label">代取员：</span>
                    <span class="value">{{ order.courierName || '未知' }}</span>
                  </div>
                  <div class="info-item" v-if="order.description">
                    <span class="label">快递描述：</span>
                    <span class="value">{{ order.description }}</span>
                  </div>
                </el-col>
              </el-row>
            </div>
            
            <el-divider />
            
            <div class="order-actions">
              <template v-if="order.status === orderStore.ORDER_STATUS.PENDING">
                <el-button type="danger" plain @click="handleCancelOrder(order)">取消订单</el-button>
                <el-button type="primary" plain @click="handleEditOrder(order)">修改订单</el-button>
              </template>
              
              <template v-else-if="order.status === orderStore.ORDER_STATUS.ACCEPTED">
                <el-button type="warning" plain @click="handleContactCourier(order)">联系代取员</el-button>
                <el-button type="danger" plain @click="handleCancelOrder(order)">取消订单</el-button>
              </template>
              
              <template v-else-if="order.status === orderStore.ORDER_STATUS.DELIVERED">
                <el-button type="success" @click="handleConfirmReceived(order)">确认收货</el-button>
                <el-button type="primary" plain @click="handleContactCourier(order)">联系代取员</el-button>
              </template>
              
              <template v-else-if="order.status === orderStore.ORDER_STATUS.COMPLETED">
                <el-button type="primary" plain @click="handleRate(order)">评价代取员</el-button>
                <el-button plain @click="handleReorder(order)">再次下单</el-button>
              </template>
              
              <el-button type="info" plain @click="handleViewDetails(order)">查看详情</el-button>
            </div>
          </el-card>
        </div>
      </div>
    </el-card>
    
    <!-- 确认收货对话框 -->
    <el-dialog v-model="confirmDialogVisible" title="确认收货" width="400px">
      <p>确认已收到快递吗？确认后将支付剩余费用给代取员。</p>
      <template #footer>
        <el-button @click="confirmDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmReceived">确认收货</el-button>
      </template>
    </el-dialog>
    
    <!-- 评价对话框 -->
    <el-dialog v-model="rateDialogVisible" title="评价代取员" width="500px">
      <el-form :model="rateForm" label-width="80px">
        <el-form-item label="服务评分">
          <el-rate v-model="rateForm.rating" :max="5" show-text />
        </el-form-item>
        <el-form-item label="评价内容">
          <el-input
            v-model="rateForm.comment"
            type="textarea"
            :rows="4"
            placeholder="请对代取员的服务进行评价"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="rateDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitRating">提交评价</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useOrderStore } from '../../stores/order.js'
import { useAuthStore } from '../../stores/auth.js'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'

const router = useRouter()
const orderStore = useOrderStore()
const authStore = useAuthStore()

const activeTab = ref('all')
const confirmDialogVisible = ref(false)
const rateDialogVisible = ref(false)
const currentOrder = ref(null)

const rateForm = reactive({
  rating: 5,
  comment: ''
})

const userOrders = computed(() => {
  return orderStore.getOrdersByUser(authStore.user?.id).sort((a, b) => 
    new Date(b.createTime) - new Date(a.createTime)
  )
})

const filteredOrders = computed(() => {
  switch (activeTab.value) {
    case 'pending':
      return userOrders.value.filter(order => order.status === orderStore.ORDER_STATUS.PENDING)
    case 'processing':
      return userOrders.value.filter(order => 
        [orderStore.ORDER_STATUS.ACCEPTED, orderStore.ORDER_STATUS.PICKED_UP, 
         orderStore.ORDER_STATUS.DELIVERING, orderStore.ORDER_STATUS.DELIVERED].includes(order.status)
      )
    case 'completed':
      return userOrders.value.filter(order => order.status === orderStore.ORDER_STATUS.COMPLETED)
    case 'cancelled':
      return userOrders.value.filter(order => order.status === orderStore.ORDER_STATUS.CANCELLED)
    default:
      return userOrders.value
  }
})

const formatTime = (time) => {
  return new Date(time).toLocaleString()
}

const getStatusType = (status) => {
  const statusMap = {
    [orderStore.ORDER_STATUS.PENDING]: 'warning',
    [orderStore.ORDER_STATUS.ACCEPTED]: 'primary',
    [orderStore.ORDER_STATUS.PICKED_UP]: 'primary',
    [orderStore.ORDER_STATUS.DELIVERING]: 'primary',
    [orderStore.ORDER_STATUS.DELIVERED]: 'success',
    [orderStore.ORDER_STATUS.COMPLETED]: 'success',
    [orderStore.ORDER_STATUS.CANCELLED]: 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    [orderStore.ORDER_STATUS.PENDING]: '待接单',
    [orderStore.ORDER_STATUS.ACCEPTED]: '已接单',
    [orderStore.ORDER_STATUS.PICKED_UP]: '已取件',
    [orderStore.ORDER_STATUS.DELIVERING]: '配送中',
    [orderStore.ORDER_STATUS.DELIVERED]: '已送达',
    [orderStore.ORDER_STATUS.COMPLETED]: '已完成',
    [orderStore.ORDER_STATUS.CANCELLED]: '已取消'
  }
  return statusMap[status] || '未知状态'
}

const handleTabChange = (tabName) => {
  activeTab.value = tabName
}

const handleCancelOrder = async (order) => {
  try {
    await ElMessageBox.confirm('确定要取消这个订单吗？', '取消订单', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    orderStore.updateOrderStatus(order.id, orderStore.ORDER_STATUS.CANCELLED)
    ElMessage.success('订单已取消')
  } catch {
    // 用户取消
  }
}

const handleEditOrder = (order) => {
  ElMessage.info('编辑订单功能开发中')
}

const handleContactCourier = (order) => {
  ElMessage.info('联系代取员功能开发中')
}

const handleConfirmReceived = (order) => {
  currentOrder.value = order
  confirmDialogVisible.value = true
}

const confirmReceived = () => {
  orderStore.updateOrderStatus(currentOrder.value.id, orderStore.ORDER_STATUS.COMPLETED)
  confirmDialogVisible.value = false
  ElMessage.success('确认收货成功，感谢使用！')
}

const handleRate = (order) => {
  currentOrder.value = order
  rateForm.rating = 5
  rateForm.comment = ''
  rateDialogVisible.value = true
}

const submitRating = () => {
  // 这里可以保存评价到订单中
  rateDialogVisible.value = false
  ElMessage.success('评价提交成功')
}

const handleReorder = (order) => {
  // 跳转到创建订单页面，并预填信息
  router.push({
    path: '/student/create-order',
    query: {
      expressPoint: order.expressPoint,
      fee: order.fee
    }
  })
}

const handleViewDetails = (order) => {
  ElMessage.info('查看详情功能开发中')
}

onMounted(() => {
  // 初始化时可以添加一些模拟数据
  if (userOrders.value.length === 0) {
    // 添加一些示例订单用于演示
    const sampleOrders = [
      {
        expressPoint: 'main_cainiao',
        expressPointName: '本部菜鸟驿站',
        pickupCode: 'CN123456789',
        expectedTime: new Date(Date.now() + 2 * 60 * 60 * 1000),
        fee: 5,
        description: '中等大小包裹',
        campus: 'main',
        dormBuilding: '1号楼',
        roomNumber: '101',
        contactPhone: '13800138000',
        deliveryAddress: '本部校区 1号楼 101',
        userId: authStore.user?.id
      }
    ]
    
    sampleOrders.forEach(orderData => {
      orderStore.createOrder(orderData)
    })
  }
})
</script>

<style scoped>
.orders-container {
  width: 100%;
  height: 100%;
  padding: 8px;
  overflow-y: auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: #303133;
}

.orders-list {
  margin-top: 20px;
}

.order-card {
  margin-bottom: 20px;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.order-info h4 {
  margin: 0 0 5px 0;
  color: #303133;
}

.order-time {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.order-content {
  margin: 15px 0;
}

.info-item {
  display: flex;
  margin-bottom: 8px;
}

.label {
  color: #909399;
  width: 80px;
  flex-shrink: 0;
}

.value {
  color: #303133;
  flex: 1;
}

.value.fee {
  color: #e6a23c;
  font-weight: bold;
}

.order-actions {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.empty-state {
  text-align: center;
  padding: 40px 0;
}
</style>