<template>
  <div class="image-upload-container">
    <el-upload
      class="image-uploader"
      :show-file-list="false"
      :before-upload="beforeUpload"
      :on-change="handleChange"
      :auto-upload="false"
      accept="image/*"
      drag
    >
      <div v-if="displayUrl" class="image-preview">
        <img :src="displayUrl" alt="预览图片" class="preview-image" />
        <div class="image-overlay">
          <div class="overlay-actions">
            <el-button
              type="primary"
              :icon="Upload"
              @click.stop="handleUpload"
              :loading="loading"
              size="small"
            >
              {{ loading ? '上传中...' : '上传图片' }}
            </el-button>
            <el-button
              type="info"
              :icon="ZoomIn"
              @click.stop="handlePreview"
              size="small"
            >
              预览
            </el-button>
            <el-button
              type="danger"
              :icon="Delete"
              @click.stop="handleRemove"
              size="small"
            >
              删除
            </el-button>
          </div>
        </div>
      </div>
      <div v-else class="upload-placeholder">
        <el-icon class="upload-icon"><Plus /></el-icon>
        <div class="upload-text">
          <p>点击或拖拽图片到此处上传</p>
          <p class="upload-hint">支持 JPG、PNG 格式，大小不超过 {{ maxSize }}MB</p>
        </div>
      </div>
    </el-upload>
    
    <!-- 上传进度 -->
    <el-progress
      v-if="loading"
      :percentage="uploadProgress"
      :show-text="false"
      class="upload-progress"
    />
    
    <!-- 图片预览对话框 -->
    <el-dialog
      v-model="previewVisible"
      title="图片预览"
      width="60%"
      :before-close="closePreview"
      append-to-body
    >
      <div class="preview-dialog-content">
        <img 
          :src="displayUrl" 
          alt="预览图片" 
          class="preview-dialog-image"
          @load="handlePreviewLoad"
          @error="handlePreviewError"
        />
      </div>
      <template #footer>
        <div class="preview-dialog-footer">
          <el-button @click="closePreview">关闭</el-button>
          <el-button type="primary" @click="downloadImage">下载图片</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, watch, defineProps, defineEmits } from 'vue'
import { ElMessage } from 'element-plus'
import { Upload, Delete, Plus, ZoomIn } from '@element-plus/icons-vue'
import { useImageHandler } from '../../composables/useImageHandler.js'

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  maxSize: {
    type: Number,
    default: 2
  },
  allowedTypes: {
    type: Array,
    default: () => ['image/jpeg', 'image/png']
  },
  uploadPath: {
    type: String,
    default: '/images'
  },
  width: {
    type: String,
    default: '200px'
  },
  height: {
    type: String,
    default: '200px'
  }
})

const emit = defineEmits(['update:modelValue', 'upload-success', 'upload-error'])

const {
  loading,
  displayUrl,
  selectedFile,
  loadImage,
  uploadImage,
  deleteImage,
  validateImage,
  handleFileChange,
  generateFilePath,
  reset
} = useImageHandler()

const uploadProgress = ref(0)
const previewVisible = ref(false)

// 监听外部传入的图片路径
watch(() => props.modelValue, async (newValue) => {
  if (newValue && newValue !== displayUrl.value) {
    try {
      await loadImage(newValue)
    } catch (error) {
      console.error('加载图片失败:', error)
    }
  } else if (!newValue) {
    reset()
  }
}, { immediate: true })

// 文件上传前验证
const beforeUpload = (file) => {
  return validateImage(file, {
    allowedTypes: props.allowedTypes,
    maxSize: props.maxSize
  })
}

// 文件选择变化处理
const handleChange = (fileInfo) => {
  const success = handleFileChange(fileInfo, {
    allowedTypes: props.allowedTypes,
    maxSize: props.maxSize
  })
  
  if (!success) {
    return false
  }
  
  // 模拟上传进度
  uploadProgress.value = 0
}

// 上传图片
const handleUpload = async () => {
  if (!selectedFile.value) {
    ElMessage.warning('请先选择图片')
    return
  }
  
  try {
    // 生成唯一文件路径
    const filePath = generateFilePath(selectedFile.value.name, props.uploadPath.replace('/', ''))
    
    // 模拟上传进度
    const progressInterval = setInterval(() => {
      if (uploadProgress.value < 90) {
        uploadProgress.value += 10
      }
    }, 100)
    
    // 上传图片
    const uploadedPath = await uploadImage(selectedFile.value, filePath, props.modelValue)
    
    clearInterval(progressInterval)
    uploadProgress.value = 100
    
    // 发送事件
    emit('update:modelValue', uploadedPath)
    emit('upload-success', uploadedPath)
    
    ElMessage.success('图片上传成功')
    
    // 重置进度
    setTimeout(() => {
      uploadProgress.value = 0
    }, 1000)
    
  } catch (error) {
    uploadProgress.value = 0
    emit('upload-error', error)
    console.error('上传失败:', error)
  }
}

// 删除图片
const handleRemove = async () => {
  try {
    if (props.modelValue) {
      await deleteImage(props.modelValue)
    }
    
    reset()
    emit('update:modelValue', '')
    ElMessage.success('图片删除成功')
  } catch (error) {
    console.error('删除失败:', error)
    ElMessage.error('删除图片失败')
  }
}

// 预览图片
const handlePreview = () => {
  if (displayUrl.value) {
    previewVisible.value = true
  } else {
    ElMessage.warning('没有可预览的图片')
  }
}

// 关闭预览
const closePreview = () => {
  previewVisible.value = false
}

// 预览图片加载成功
const handlePreviewLoad = () => {
  console.log('预览图片加载成功')
}

// 预览图片加载失败
const handlePreviewError = () => {
  ElMessage.error('预览图片加载失败')
}

// 下载图片
const downloadImage = () => {
  if (displayUrl.value && props.modelValue) {
    try {
      const link = document.createElement('a')
      link.href = displayUrl.value
      link.download = props.modelValue.split('/').pop() || 'image'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      ElMessage.success('图片下载成功')
    } catch (error) {
      console.error('下载失败:', error)
      ElMessage.error('图片下载失败')
    }
  }
}
</script>

<style scoped>
.image-upload-container {
  position: relative;
}

.image-uploader {
  width: v-bind(width);
  height: v-bind(height);
}

.image-uploader :deep(.el-upload) {
  width: 100%;
  height: 100%;
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.image-uploader :deep(.el-upload:hover) {
  border-color: #409eff;
}

.image-uploader :deep(.el-upload-dragger) {
  width: 100%;
  height: 100%;
  background: transparent;
  border: none;
  border-radius: 8px;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #8c939d;
}

.upload-icon {
  font-size: 48px;
  margin-bottom: 16px;
  color: #c0c4cc;
}

.upload-text {
  text-align: center;
}

.upload-text p {
  margin: 4px 0;
}

.upload-hint {
  font-size: 12px;
  color: #a8abb2;
}

.image-preview {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  border-radius: 8px;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.image-preview:hover .image-overlay {
  opacity: 1;
}

.overlay-actions {
  display: flex;
  gap: 8px;
}

.upload-progress {
  margin-top: 8px;
}

/* 预览对话框样式 */
.preview-dialog-content {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  background: #f5f7fa;
  border-radius: 8px;
  padding: 20px;
}

.preview-dialog-image {
  max-width: 100%;
  max-height: 70vh;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  background: white;
}

.preview-dialog-footer {
  display: flex;
  justify-content: center;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .overlay-actions {
    flex-direction: column;
    gap: 4px;
  }
  
  .overlay-actions .el-button {
    width: 100%;
    font-size: 12px;
    padding: 4px 8px;
  }
  
  .preview-dialog-image {
    max-height: 60vh;
  }
}
</style>