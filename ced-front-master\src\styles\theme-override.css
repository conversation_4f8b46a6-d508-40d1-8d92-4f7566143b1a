/* 现代化主题覆盖 */

/* 保持现代渐变效果 */
.gradient-bg {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 现代按钮样式 */
.el-button--primary,
.el-button--primary:hover,
.el-button--primary:focus {
  background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%) !important;
  border-color: #2563eb !important;
  color: #ffffff !important;
  box-shadow: 0 2px 4px rgba(37, 99, 235, 0.2) !important;
}

.el-button--success,
.el-button--success:hover,
.el-button--success:focus {
  background: linear-gradient(135deg, #10b981 0%, #34d399 100%) !important;
  border-color: #10b981 !important;
  color: #ffffff !important;
  box-shadow: 0 2px 4px rgba(16, 185, 129, 0.2) !important;
}

.el-button--warning,
.el-button--warning:hover,
.el-button--warning:focus {
  background: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%) !important;
  border-color: #f59e0b !important;
  color: #ffffff !important;
  box-shadow: 0 2px 4px rgba(245, 158, 11, 0.2) !important;
}

.el-button--danger,
.el-button--danger:hover,
.el-button--danger:focus {
  background: linear-gradient(135deg, #ef4444 0%, #f87171 100%) !important;
  border-color: #ef4444 !important;
  color: #ffffff !important;
  box-shadow: 0 2px 4px rgba(239, 68, 68, 0.2) !important;
}

/* 现代标签颜色 */
.el-tag--primary {
  background-color: #dbeafe !important;
  border-color: #2563eb !important;
  color: #2563eb !important;
}

.el-tag--success {
  background-color: #d1fae5 !important;
  border-color: #10b981 !important;
  color: #10b981 !important;
}

.el-tag--warning {
  background-color: #fef3c7 !important;
  border-color: #f59e0b !important;
  color: #f59e0b !important;
}

.el-tag--danger {
  background-color: #fee2e2 !important;
  border-color: #ef4444 !important;
  color: #ef4444 !important;
}

.el-tag--info {
  background-color: #f3f4f6 !important;
  border-color: #6b7280 !important;
  color: #6b7280 !important;
}

/* 统一进度条颜色 */
.el-progress-bar__inner {
  background-color: #000000 !important;
}

/* 统一评分颜色 */
.el-rate__icon.is-active {
  color: #000000 !important;
}

/* 统一链接颜色 */
.el-link--primary {
  color: #000000 !important;
}

.el-link--primary:hover {
  color: #333333 !important;
}

/* 统一菜单激活状态 */
.el-menu-item.is-active {
  background-color: #000000 !important;
  color: #ffffff !important;
}

/* 统一选项卡激活状态 */
.el-tabs__active-bar {
  background-color: #000000 !important;
}

.el-tabs__item.is-active {
  color: #000000 !important;
}

/* 统一分页激活状态 */
.el-pagination .el-pager li.is-active {
  background-color: #000000 !important;
  color: #ffffff !important;
  border-color: #000000 !important;
}

/* 统一步骤条颜色 */
.el-steps .el-step__head.is-process,
.el-steps .el-step__head.is-finish {
  color: #000000 !important;
  border-color: #000000 !important;
}

.el-steps .el-step__head.is-finish .el-step__icon,
.el-steps .el-step__head.is-process .el-step__icon {
  background-color: #000000 !important;
  color: #ffffff !important;
}

/* 统一时间线颜色 */
.el-timeline-item__node {
  background-color: #000000 !important;
  border-color: #000000 !important;
}

/* 统一开关颜色 */
.el-switch.is-checked .el-switch__core {
  background-color: #000000 !important;
  border-color: #000000 !important;
}

/* 统一滑块颜色 */
.el-slider__bar {
  background-color: #000000 !important;
}

.el-slider__button {
  border-color: #000000 !important;
}

/* 统一输入框焦点颜色 */
.el-input__wrapper.is-focus {
  border-color: #000000 !important;
  box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1) !important;
}

/* 统一选择器下拉箭头颜色 */
.el-select .el-input.is-focus .el-input__wrapper {
  border-color: #000000 !important;
}

/* 统一日期选择器颜色 */
.el-date-editor.el-input__wrapper.is-focus {
  border-color: #000000 !important;
}

/* 统一数字输入框按钮颜色 */
.el-input-number__increase:hover,
.el-input-number__decrease:hover {
  color: #000000 !important;
}

/* 统一上传组件颜色 */
.el-upload-dragger:hover {
  border-color: #000000 !important;
}

/* 统一表格选中行颜色 */
.el-table__row.current-row > td.el-table__cell {
  background-color: #f5f5f5 !important;
}

/* 统一消息提示颜色 */
.el-message--success {
  background-color: #000000 !important;
  border-color: #000000 !important;
  color: #ffffff !important;
}

.el-message--warning {
  background-color: #666666 !important;
  border-color: #666666 !important;
  color: #ffffff !important;
}

.el-message--error {
  background-color: #999999 !important;
  border-color: #999999 !important;
  color: #ffffff !important;
}

/* 移除所有阴影效果，使用简单边框 */
.el-card,
.el-dialog,
.el-popover,
.el-tooltip__popper,
.el-select-dropdown {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
  border: 1px solid #e5e5e5 !important;
}

/* 统一所有背景为白色或浅灰 */
.el-card,
.el-dialog,
.el-popover,
.el-tooltip__popper,
.el-select-dropdown,
.el-menu,
.el-table {
  background-color: #ffffff !important;
}

.el-card__header,
.el-dialog__header,
.el-table th.el-table__cell {
  background-color: #fafafa !important;
}

/* 统一所有文字颜色 */
.el-card__header,
.el-dialog__title,
.el-table th.el-table__cell {
  color: #333333 !important;
}

/* 移除所有动画效果，保持简洁 */
* {
  transition: none !important;
  animation: none !important;
}

/* 只保留基本的hover效果 */
.el-button:hover,
.el-menu-item:hover,
.el-table__row:hover > td.el-table__cell {
  transition: background-color 0.2s ease !important;
}

/* 确保所有图标颜色统一 */
.el-icon {
  color: inherit !important;
}

/* 统一所有边框颜色 */
.el-input__wrapper,
.el-select .el-input__wrapper,
.el-textarea__inner,
.el-card,
.el-table,
.el-table th.el-table__cell,
.el-table td.el-table__cell {
  border-color: #e5e5e5 !important;
}

/* 确保所有文本颜色正确 */
body,
.el-table,
.el-card,
.el-dialog {
  color: #333333 !important;
}

/* 统一所有占位符颜色 */
.el-input__inner::placeholder,
.el-textarea__inner::placeholder {
  color: #cccccc !important;
}