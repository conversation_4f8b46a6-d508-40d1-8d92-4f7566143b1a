<template>
  <div class="home-container">
    <!-- 欢迎横幅 -->
    <div class="welcome-banner">
      <div class="banner-content">
        <div class="banner-text">
          <h2>欢迎回来，{{ authStore.user?.realName || '同学' }}</h2>
          <p>今天是个发布代取订单的好日子</p>
        </div>
        <div class="banner-action">
          <el-button type="primary" size="large" @click="$router.push('/student/create-order')">
            <el-icon><Plus /></el-icon>
            发布订单
          </el-button>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="24" class="stats-row">
      <el-col :span="8">
        <el-card class="stat-card stat-card-primary" shadow="hover">
          <div class="stat-content">
            <div class="stat-info">
              <div class="stat-number">{{ stats.totalOrders }}</div>
              <div class="stat-label">总订单数</div>
              <div class="stat-trend">
                <el-icon><ArrowUp /></el-icon>
                <span>较上月 +12%</span>
              </div>
            </div>
            <div class="stat-icon-wrapper">
              <el-icon size="24"><List /></el-icon>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="stat-card stat-card-warning" shadow="hover">
          <div class="stat-content">
            <div class="stat-info">
              <div class="stat-number">{{ stats.pendingOrders }}</div>
              <div class="stat-label">进行中订单</div>
              <div class="stat-trend">
                <el-icon><Clock /></el-icon>
                <span>需要关注</span>
              </div>
            </div>
            <div class="stat-icon-wrapper">
              <el-icon size="24"><Clock /></el-icon>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="stat-card stat-card-success" shadow="hover">
          <div class="stat-content">
            <div class="stat-info">
              <div class="stat-number">¥{{ stats.totalSpent }}</div>
              <div class="stat-label">累计消费</div>
              <div class="stat-trend">
                <el-icon><ArrowUp /></el-icon>
                <span>本月节省 ¥15</span>
              </div>
            </div>
            <div class="stat-icon-wrapper">
              <el-icon size="24"><Money /></el-icon>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="24" style="margin-top: 24px;">
      <!-- 快速操作 -->
      <el-col :span="16">
        <el-card class="quick-actions-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <div class="header-title">
                <el-icon><Star /></el-icon>
                <span>快速操作</span>
              </div>
            </div>
          </template>
          <div class="quick-actions-grid">
            <div class="action-item" @click="$router.push('/student/create-order')">
              <div class="action-icon primary">
                <el-icon size="20"><Plus /></el-icon>
              </div>
              <div class="action-content">
                <h4>发布代取</h4>
                <p>快速发布新的代取订单</p>
              </div>
              <el-icon class="action-arrow"><ArrowRight /></el-icon>
            </div>
            <div class="action-item" @click="$router.push('/student/orders')">
              <div class="action-icon success">
                <el-icon size="20"><List /></el-icon>
              </div>
              <div class="action-content">
                <h4>我的订单</h4>
                <p>查看和管理所有订单</p>
              </div>
              <el-icon class="action-arrow"><ArrowRight /></el-icon>
            </div>
            <div class="action-item" @click="$router.push('/student/profile')">
              <div class="action-icon warning">
                <el-icon size="20"><User /></el-icon>
              </div>
              <div class="action-content">
                <h4>个人中心</h4>
                <p>管理个人信息和设置</p>
              </div>
              <el-icon class="action-arrow"><ArrowRight /></el-icon>
            </div>
            <div class="action-item">
              <div class="action-icon info">
                <el-icon size="20"><Present /></el-icon>
              </div>
              <div class="action-content">
                <h4>优惠活动</h4>
                <p>查看最新优惠和活动</p>
              </div>
              <el-icon class="action-arrow"><ArrowRight /></el-icon>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <!-- 最近订单 -->
      <el-col :span="8">
        <el-card class="recent-orders-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <div class="header-title">
                <el-icon><Bell /></el-icon>
                <span>最近订单</span>
              </div>
              <el-link type="primary" @click="$router.push('/student/orders')">查看全部</el-link>
            </div>
          </template>
          <div class="recent-orders">
            <div v-if="recentOrders.length === 0" class="empty-state">
              <el-empty description="暂无订单" :image-size="80">
                <el-button type="primary" @click="$router.push('/student/create-order')">
                  发布第一个订单
                </el-button>
              </el-empty>
            </div>
            <div v-else class="orders-list">
              <div v-for="order in recentOrders" :key="order.orderId" class="order-item">
                <div class="order-icon">
                  <el-icon size="20"><Box /></el-icon>
                </div>
                <div class="order-info">
                  <div class="order-title">{{ order.stationName || '快递站点' }}</div>
                  <div class="order-details">
                    <span class="order-code">取件码：{{ order.pickupCode }}</span>
                    <span class="order-time">{{ formatTime(order.createTime) }}</span>
                  </div>
                </div>
                <div class="order-status">
                  <el-tag :type="getStatusType(order.orderStatus)" size="small">
                    {{ getStatusText(order.orderStatus) }}
                  </el-tag>
                  <div class="order-fee">¥{{ order.serviceFee }}</div>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>


  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useAuthStore } from '../../stores/auth.js'
import { getMyOrdersApi } from '../../request/orderApi.js'
import { 
  List, Clock, Money, Plus, ArrowUp,
  Bell, Star, Present, ArrowRight, User, Box
} from '@element-plus/icons-vue'

const authStore = useAuthStore()

const stats = ref({
  totalOrders: 0,
  pendingOrders: 0,
  totalSpent: 0
})

const recentOrders = ref([])

const formatTime = (time) => {
  return new Date(time).toLocaleString()
}

const getStatusType = (status) => {
  switch (status) {
    case 1: return 'warning'
    case 2: case 3: case 4: case 5: return 'primary'
    case 6: return 'success'
    case 7: return 'danger'
    default: return 'info'
  }
}

const getStatusText = (status) => {
  switch (status) {
    case 1: return '待接单'
    case 2: return '已接单'
    case 3: return '取件中'
    case 4: return '配送中'
    case 5: return '待确认'
    case 6: return '已完成'
    case 7: return '已取消'
    default: return '未知状态'
  }
}

const loadData = async () => {
  try {
    const result = await getMyOrdersApi()
    if (result.code === 200) {
      const orders = result.data || []
      recentOrders.value = orders.slice(0, 3)
      
      // 计算统计数据
      stats.value.totalOrders = orders.length
      stats.value.pendingOrders = orders.filter(order => 
        [1, 2, 3, 4, 5].includes(order.orderStatus)
      ).length
      stats.value.totalSpent = orders.reduce((total, order) => total + (order.serviceFee || 0), 0)
    }
  } catch (error) {
    console.error('加载数据失败:', error)
  }
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.home-container {
  width: 100%;
  height: 100%;
  padding: 8px;
  overflow-y: auto;
}

.welcome-banner {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  padding: 32px;
  margin-bottom: 24px;
  color: white;
  position: relative;
  overflow: hidden;
}

.welcome-banner::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
  animation: rotate 20s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.banner-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 1;
}

.banner-text h2 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
}

.banner-text p {
  margin: 0;
  font-size: 16px;
  opacity: 0.9;
}

.banner-action .el-button {
  font-size: 16px;
  padding: 12px 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.stats-row {
  margin-bottom: 24px;
}

.stat-card {
  border-radius: 16px;
  border: none;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #2563eb, #3b82f6);
}

.stat-card-primary::before {
  background: linear-gradient(90deg, #2563eb, #3b82f6);
}

.stat-card-warning::before {
  background: linear-gradient(90deg, #f59e0b, #fbbf24);
}

.stat-card-success::before {
  background: linear-gradient(90deg, #10b981, #34d399);
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stat-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 0 10px 0;
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 32px;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 8px;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

.stat-icon-wrapper {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
}

.stat-card-primary .stat-icon-wrapper {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  color: #2563eb;
}

.stat-card-warning .stat-icon-wrapper {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  color: #f59e0b;
}

.stat-card-success .stat-icon-wrapper {
  background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
  color: #10b981;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #10b981;
  margin-top: 8px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #1a1a1a;
}

.quick-actions-card {
  border-radius: 16px;
  border: none;
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.action-item {
  display: flex;
  align-items: center;
  padding: 20px;
  border-radius: 12px;
  background: #f8fafc;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #f1f5f9;
}

.action-item:hover {
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.action-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  color: white;
}

.action-icon.primary {
  background: linear-gradient(135deg, #2563eb, #3b82f6);
}

.action-icon.success {
  background: linear-gradient(135deg, #10b981, #34d399);
}

.action-icon.warning {
  background: linear-gradient(135deg, #f59e0b, #fbbf24);
}

.action-icon.info {
  background: linear-gradient(135deg, #6b7280, #9ca3af);
}

.action-content {
  flex: 1;
}

.action-content h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
}

.action-content p {
  margin: 0;
  font-size: 14px;
  color: #6b7280;
}

.action-arrow {
  color: #d1d5db;
  transition: all 0.3s ease;
}

.action-item:hover .action-arrow {
  color: #2563eb;
  transform: translateX(4px);
}

.recent-orders-card {
  border-radius: 16px;
  border: none;
}

.orders-list {
  max-height: 300px;
  overflow-y: auto;
}

.order-item {
  display: flex;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #f3f4f6;
  transition: all 0.3s ease;
}

.order-item:last-child {
  border-bottom: none;
}

.order-item:hover {
  background: #f8fafc;
  margin: 0 -16px;
  padding: 16px;
  border-radius: 8px;
}

.order-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #dbeafe, #bfdbfe);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #2563eb;
  margin-right: 16px;
}

.order-info {
  flex: 1;
}

.order-title {
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.order-details {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #6b7280;
}

.order-status {
  text-align: right;
}

.order-fee {
  font-size: 14px;
  font-weight: 600;
  color: #10b981;
  margin-top: 4px;
}



.empty-state {
  text-align: center;
  padding: 40px 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .quick-actions-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .banner-content {
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }
  
  .stats-row .el-col {
    margin-bottom: 16px;
  }
}
</style>