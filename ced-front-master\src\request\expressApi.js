import request from './request'

// 获取快递站点列表
export const getStationsApi = () => {
  return request.get('/express/stations')
}

// 根据校区获取快递站点
export const getStationsByCampusApi = (campus) => {
  return request.get(`/express/stations/campus/${campus}`)
}

// 获取站点详情
export const getStationDetailApi = (stationId) => {
  return request.get(`/express/stations/${stationId}`)
}

// 获取用户地址列表
export const getUserAddressesApi = () => {
  return request.get('/express/addresses')
}

// 添加地址
export const addAddressApi = (data) => {
  return request.post('/express/addresses', data)
}

// 更新地址
export const updateAddressApi = (addressId, data) => {
  return request.put(`/express/addresses/${addressId}`, data)
}

// 删除地址
export const deleteAddressApi = (addressId) => {
  return request.delete(`/express/addresses/${addressId}`)
}

// 设置默认地址
export const setDefaultAddressApi = (addressId) => {
  return request.put(`/express/addresses/${addressId}/default`)
}