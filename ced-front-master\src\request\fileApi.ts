import request from "./request";
import { ReportData } from "@/types/api";

export const uploadFileApi = (data: { file: File, path: string }): Promise<ReportData<any>> => {
    const formData = new FormData();
    formData.append('file', data.file);
    formData.append('path', data.path);
    return request.post('/file/upload', formData);
}

export const downloadFile = (path: string): Promise<ReportData<Blob>> => {
    return request.get("/file/download", {
        params: { path },
        responseType: 'blob'
    });
};

export const deleteFileApi = (data: { path: string }): Promise<ReportData<any>> => {
    return request.post('/file/delete', data)
}