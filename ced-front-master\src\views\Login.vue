<template>
  <div class="login-container">
    <div class="login-content">
      <!-- 品牌区域 -->
      <div class="brand-section">
        <el-tooltip content="校园快递代取系统" placement="right" :show-after="500">
          <div class="logo">
            <el-icon size="40"><Van /></el-icon>
          </div>
        </el-tooltip>
        <h1 class="brand-title">校园快递代取</h1>
        <p class="brand-subtitle">便捷、安全、高效的校园快递服务</p>
      </div>

      <!-- 登录表单 -->
      <div class="form-section">
        <!-- 用户类型选择 -->
        <div class="user-type-selector">
          <el-tooltip content="学生登录 - 使用学号登录" placement="top" :show-after="300">
            <div 
              class="type-card" 
              :class="{ active: activeTab === 'student' }"
              @click="switchUserType('student')"
            >
              <div class="type-icon student">
                <el-icon size="20"><User /></el-icon>
              </div>
              <div class="type-info">
                <h4>学生</h4>
              </div>
            </div>
          </el-tooltip>
          
          <el-tooltip content="代取员登录 - 使用手机号登录" placement="top" :show-after="300">
            <div 
              class="type-card" 
              :class="{ active: activeTab === 'courier' }"
              @click="switchUserType('courier')"
            >
              <div class="type-icon courier">
                <el-icon size="20"><Van /></el-icon>
              </div>
              <div class="type-info">
                <h4>代取员</h4>
              </div>
            </div>
          </el-tooltip>
          
          <el-tooltip content="管理员登录 - 系统管理登录" placement="top" :show-after="300">
            <div 
              class="type-card" 
              :class="{ active: activeTab === 'admin' }"
              @click="switchUserType('admin')"
            >
              <div class="type-icon admin">
                <el-icon size="20"><UserFilled /></el-icon>
              </div>
              <div class="type-info">
                <h4>管理员</h4>
              </div>
            </div>
          </el-tooltip>
        </div>

        <!-- 登录表单卡片 -->
        <el-card class="login-card">
          <!-- 学生登录表单 -->
          <div v-if="activeTab === 'student'" class="login-form-container">
            <div class="form-header">
              <el-tooltip content="学生用户" placement="top">
                <el-icon size="24" color="#2563eb"><User /></el-icon>
              </el-tooltip>
              <h3>学生登录</h3>
            </div>
            <el-form :model="studentForm" :rules="rules" ref="studentFormRef" class="login-form">
              <el-form-item prop="studentId">
                <el-input
                  v-model="studentForm.studentId"
                  placeholder="请输入学号"
                  size="large"
                  clearable
                >
                  <template #prefix>
                    <el-icon><User /></el-icon>
                  </template>
                </el-input>
              </el-form-item>
              <el-form-item prop="password">
                <el-input
                  v-model="studentForm.password"
                  type="password"
                  placeholder="请输入密码"
                  size="large"
                  show-password
                  clearable
                >
                  <template #prefix>
                    <el-icon><Lock /></el-icon>
                  </template>
                </el-input>
              </el-form-item>
              <el-form-item prop="code">
                <div class="captcha-container">
                  <el-input
                    v-model="studentForm.code"
                    placeholder="请输入验证码"
                    size="large"
                    clearable
                    maxlength="4"
                    class="captcha-input"
                  >
                    <template #prefix>
                      <el-icon><Van /></el-icon>
                    </template>
                  </el-input>
                  <div class="captcha-image-container">
                    <img 
                      :src="captchaData.img" 
                      alt="验证码" 
                      class="captcha-image"
                      @click="refreshCaptcha"
                      title="点击刷新验证码"
                    />
                    <el-button 
                      type="text" 
                      @click="refreshCaptcha"
                      class="refresh-btn"
                      title="刷新验证码"
                    >
                      <el-icon><Refresh /></el-icon>
                    </el-button>
                  </div>
                </div>
              </el-form-item>
              <el-form-item>
                <el-button
                  type="primary"
                  @click="handleLogin('student')"
                  :loading="loading"
                  size="large"
                  class="login-btn"
                >
                  登录
                </el-button>
              </el-form-item>
            </el-form>
          </div>

          <!-- 代取员登录表单 -->
          <div v-if="activeTab === 'courier'" class="login-form-container">
            <div class="form-header">
              <el-tooltip content="代取员用户" placement="top">
                <el-icon size="24" color="#10b981"><Van /></el-icon>
              </el-tooltip>
              <h3>代取员登录</h3>
            </div>
            <el-form :model="courierForm" :rules="rules" ref="courierFormRef" class="login-form">
              <el-form-item prop="phone">
                <el-input
                  v-model="courierForm.phone"
                  placeholder="请输入手机号"
                  size="large"
                  clearable
                >
                  <template #prefix>
                    <el-icon><Phone /></el-icon>
                  </template>
                </el-input>
              </el-form-item>
              <el-form-item prop="password">
                <el-input
                  v-model="courierForm.password"
                  type="password"
                  placeholder="请输入密码"
                  size="large"
                  show-password
                  clearable
                >
                  <template #prefix>
                    <el-icon><Lock /></el-icon>
                  </template>
                </el-input>
              </el-form-item>
              <el-form-item prop="code">
                <div class="captcha-container">
                  <el-input
                    v-model="courierForm.code"
                    placeholder="请输入验证码"
                    size="large"
                    clearable
                    maxlength="4"
                    class="captcha-input"
                  >
                    <template #prefix>
                      <el-icon><Van /></el-icon>
                    </template>
                  </el-input>
                  <div class="captcha-image-container">
                    <img 
                      :src="captchaData.img" 
                      alt="验证码" 
                      class="captcha-image"
                      @click="refreshCaptcha"
                      title="点击刷新验证码"
                    />
                    <el-button 
                      type="text" 
                      @click="refreshCaptcha"
                      class="refresh-btn"
                      title="刷新验证码"
                    >
                      <el-icon><Refresh /></el-icon>
                    </el-button>
                  </div>
                </div>
              </el-form-item>
              <el-form-item>
                <el-button
                  type="success"
                  @click="handleLogin('courier')"
                  :loading="loading"
                  size="large"
                  class="login-btn"
                >
                  登录
                </el-button>
              </el-form-item>
            </el-form>
          </div>

          <!-- 管理员登录表单 -->
          <div v-if="activeTab === 'admin'" class="login-form-container">
            <div class="form-header">
              <el-tooltip content="系统管理员" placement="top">
                <el-icon size="24" color="#f59e0b"><UserFilled /></el-icon>
              </el-tooltip>
              <h3>管理员登录</h3>
            </div>
            <el-form :model="adminForm" :rules="rules" ref="adminFormRef" class="login-form">
              <el-form-item prop="username">
                <el-input
                  v-model="adminForm.username"
                  placeholder="请输入管理员账号"
                  size="large"
                  clearable
                >
                  <template #prefix>
                    <el-icon><UserFilled /></el-icon>
                  </template>
                </el-input>
              </el-form-item>
              <el-form-item prop="password">
                <el-input
                  v-model="adminForm.password"
                  type="password"
                  placeholder="请输入密码"
                  size="large"
                  show-password
                  clearable
                >
                  <template #prefix>
                    <el-icon><Lock /></el-icon>
                  </template>
                </el-input>
              </el-form-item>
              <el-form-item prop="code">
                <div class="captcha-container">
                  <el-input
                    v-model="adminForm.code"
                    placeholder="请输入验证码"
                    size="large"
                    clearable
                    maxlength="4"
                    class="captcha-input"
                  >
                    <template #prefix>
                      <el-icon><Van /></el-icon>
                    </template>
                  </el-input>
                  <div class="captcha-image-container">
                    <img 
                      :src="captchaData.img" 
                      alt="验证码" 
                      class="captcha-image"
                      @click="refreshCaptcha"
                      title="点击刷新验证码"
                    />
                    <el-button 
                      type="text" 
                      @click="refreshCaptcha"
                      class="refresh-btn"
                      title="刷新验证码"
                    >
                      <el-icon><Refresh /></el-icon>
                    </el-button>
                  </div>
                </div>
              </el-form-item>
              <el-form-item>
                <el-button
                  type="warning"
                  @click="handleLogin('admin')"
                  :loading="loading"
                  size="large"
                  class="login-btn"
                >
                  登录
                </el-button>
              </el-form-item>
            </el-form>
          </div>

          <div class="login-footer">
            <div class="footer-actions">
              <el-tooltip content="注册新账号" placement="top">
                <el-button type="text" @click="$router.push('/register')" class="footer-btn">
                  <el-icon><Plus /></el-icon>
                  立即注册
                </el-button>
              </el-tooltip>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth.js'
import { ElMessage } from 'element-plus'
import { User, Lock, Phone, UserFilled, Plus, Van, Refresh } from '@element-plus/icons-vue'
import { captchaApi, loginApi } from '../request/loginApi.ts'

const router = useRouter()
const authStore = useAuthStore()

const activeTab = ref('student')
const loading = ref(false)

// 验证码相关
const captchaData = ref({
  uuid: '',
  img: ''
})

const studentForm = reactive({
  studentId: '',
  password: '',
  code: ''
})

const courierForm = reactive({
  phone: '',
  password: '',
  code: ''
})

const adminForm = reactive({
  username: '',
  password: '',
  code: ''
})

const rules = {
  studentId: [{ required: true, message: '请输入学号', trigger: 'blur' }],
  phone: [{ required: true, message: '请输入手机号', trigger: 'blur' }],
  username: [{ required: true, message: '请输入管理员账号', trigger: 'blur' }],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
  code: [{ required: true, message: '请输入验证码', trigger: 'blur' }]
}

const studentFormRef = ref()
const courierFormRef = ref()
const adminFormRef = ref()

// 获取验证码
const getCaptcha = async () => {
  try {
    const result = await captchaApi()
    if (result.code === 200) {
      captchaData.value = result.data
    } else {
      ElMessage.error('获取验证码失败')
    }
  } catch (error) {
    console.error('获取验证码失败:', error)
    ElMessage.error('获取验证码失败')
  }
}

// 刷新验证码
const refreshCaptcha = () => {
  getCaptcha()
}

const handleLogin = async (userType) => {
  let form, formRef, username
  
  switch (userType) {
    case 'student':
      form = studentForm
      formRef = studentFormRef.value
      username = form.studentId
      break
    case 'courier':
      form = courierForm
      formRef = courierFormRef.value
      username = form.phone
      break
    case 'admin':
      form = adminForm
      formRef = adminFormRef.value
      username = form.username
      break
  }
  
  if (!formRef) return
  
  try {
    await formRef.validate()
    loading.value = true
    
    // 调用登录API
    const loginData = {
      username: username,
      password: form.password,
      code: form.code,
      uuid: captchaData.value.uuid
    }
    
    const loginRes = await loginApi(loginData)
    if (loginRes.code !== 200) {
      ElMessage.error(loginRes.msg || '登录失败')
      // 登录失败后刷新验证码
      refreshCaptcha()
      // 清空验证码输入
      form.code = ''
      return
    }
    
    // 登录成功，保存用户信息
    authStore.login(loginRes.data.userInfo, loginRes.data.token)
    
    ElMessage.success('登录成功')
    
    // 根据用户类型跳转
    const userTypeValue = loginRes.data.userInfo.userType
    if (userTypeValue === 1) {
      router.push('/student')
    } else if (userTypeValue === 2) {
      router.push('/courier')
    } else if (userTypeValue === 3) {
      router.push('/admin')
    }
  } catch (error) {
    console.error('登录失败:', error)
    ElMessage.error('登录失败，请检查网络连接')
    // 登录失败后刷新验证码
    refreshCaptcha()
    // 清空验证码输入
    form.code = ''
  } finally {
    loading.value = false
  }
}

// 切换用户类型时清空表单
const switchUserType = (type) => {
  activeTab.value = type
  // 清空所有表单的验证码输入
  studentForm.code = ''
  courierForm.code = ''
  adminForm.code = ''
  // 刷新验证码
  refreshCaptcha()
}

// 页面加载时获取验证码
onMounted(() => {
  getCaptcha()
})
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  position: relative;
  overflow: hidden;
}

.login-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="10" cy="60" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="90" cy="40" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.login-content {
  display: flex;
  align-items: center;
  gap: 60px;
  max-width: 1000px;
  width: 100%;
  position: relative;
  z-index: 1;
}

.brand-section {
  flex: 1;
  color: white;
  text-align: left;
}

.logo {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  backdrop-filter: blur(10px);
  margin-bottom: 24px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.brand-title {
  font-size: 48px;
  font-weight: 700;
  margin: 0 0 16px 0;
  line-height: 1.2;
  background: linear-gradient(135deg, #ffffff 0%, #e3f2fd 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.brand-subtitle {
  font-size: 18px;
  margin: 0;
  opacity: 0.9;
  font-weight: 300;
  line-height: 1.6;
}

.form-section {
  flex: 0 0 400px;
}

.user-type-selector {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
}

.type-card {
  flex: 1;
  padding: 16px;
  background: rgba(255, 255, 255, 0.8);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  text-align: center;
}

.type-card:hover {
  background: rgba(255, 255, 255, 0.9);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.type-card.active {
  background: rgba(255, 255, 255, 0.95);
  border-color: #2563eb;
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.2);
  transform: translateY(-2px);
}

.type-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 12px;
  color: white;
}

.type-icon.student {
  background: linear-gradient(135deg, #2563eb, #3b82f6);
}

.type-icon.courier {
  background: linear-gradient(135deg, #10b981, #34d399);
}

.type-icon.admin {
  background: linear-gradient(135deg, #f59e0b, #fbbf24);
}

.type-info h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
}

.type-info p {
  margin: 0;
  font-size: 12px;
  color: #6b7280;
}

.login-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  padding: 24px;
}

.login-card :deep(.el-card__body) {
  padding: 0;
}

.login-form-container {
  animation: fadeIn 0.3s ease-out;
}

.form-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f1f5f9;
}

.form-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #1a1a1a;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.login-form {
  padding: 0 4px;
}

.login-form .el-form-item {
  margin-bottom: 18px;
}

.login-form .el-form-item:last-child {
  margin-bottom: 0;
}

.login-btn {
  width: 100%;
  height: 48px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 12px;
}

.login-footer {
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid #f1f5f9;
}

.footer-actions {
  display: flex;
  justify-content: center;
}

.footer-btn {
  color: #64748b;
  font-weight: 500;
  font-size: 14px;
}

.footer-btn:hover {
  color: #2563eb;
}

/* 验证码样式 */
.captcha-container {
  display: flex;
  gap: 12px;
  align-items: center;
}

.captcha-input {
  flex: 1;
}

.captcha-image-container {
  position: relative;
  display: flex;
  align-items: center;
  gap: 4px;
}

.captcha-image {
  width: 100px;
  height: 40px;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #f5f7fa;
}

.captcha-image:hover {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.refresh-btn {
  padding: 8px;
  color: #606266;
  transition: all 0.3s ease;
}

.refresh-btn:hover {
  color: #409eff;
  transform: rotate(180deg);
}

.refresh-btn .el-icon {
  font-size: 16px;
}



/* 响应式设计 */
@media (max-width: 768px) {
  .login-content {
    flex-direction: column;
    gap: 40px;
    text-align: center;
  }
  
  .brand-title {
    font-size: 36px;
  }
  
  .form-section {
    flex: none;
    width: 100%;
    max-width: 400px;
  }
  
  .user-type-selector {
    flex-direction: column;
    gap: 8px;
  }
  
  .type-card {
    display: flex;
    align-items: center;
    text-align: left;
    padding: 12px 16px;
  }
  
  .type-icon {
    width: 40px;
    height: 40px;
    margin: 0 12px 0 0;
  }
  
  .type-info h4 {
    font-size: 14px;
  }
  
  .type-info p {
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .login-container {
    padding: 16px;
  }
  
  .brand-title {
    font-size: 28px;
  }
  
  .brand-subtitle {
    font-size: 16px;
  }
  
  .form-section {
    max-width: 100%;
  }
}
</style>
/
* Tooltip 自定义样式 */
:deep(.el-tooltip__popper) {
  background: rgba(0, 0, 0, 0.8) !important;
  border: none !important;
  border-radius: 8px !important;
  padding: 8px 12px !important;
  font-size: 12px !important;
  font-weight: 500 !important;
  backdrop-filter: blur(10px) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

:deep(.el-tooltip__popper .el-popper__arrow::before) {
  background: rgba(0, 0, 0, 0.8) !important;
  border: none !important;
}

/* 为不同类型的tooltip添加不同颜色 */
.type-card:hover + .el-tooltip__popper {
  background: linear-gradient(135deg, #2563eb, #3b82f6) !important;
}

/* 增强hover效果 */
.type-card:hover .type-icon {
  transform: scale(1.1);
  transition: transform 0.2s ease;
}

.form-header .el-icon:hover {
  transform: scale(1.1);
  transition: transform 0.2s ease;
}

.logo:hover .el-icon {
  transform: scale(1.05);
  transition: transform 0.2s ease;
}