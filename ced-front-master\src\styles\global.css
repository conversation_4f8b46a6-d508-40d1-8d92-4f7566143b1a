/* 现代简洁全局样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  width: 100%;
  height: 100%;
  overflow: hidden;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background-color: #fafbfc;
  color: #1a1a1a;
  font-size: 14px;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 现代设计系统变量 */
:root {
  /* 主色调 - 现代蓝色 */
  --el-color-primary: #2563eb;
  --el-color-primary-light-3: #3b82f6;
  --el-color-primary-light-5: #60a5fa;
  --el-color-primary-light-7: #93c5fd;
  --el-color-primary-light-8: #bfdbfe;
  --el-color-primary-light-9: #dbeafe;
  --el-color-primary-dark-2: #1d4ed8;
  
  /* 功能色彩 */
  --el-color-success: #10b981;
  --el-color-warning: #f59e0b;
  --el-color-danger: #ef4444;
  --el-color-info: #6b7280;
  
  /* 背景色 */
  --el-bg-color: #ffffff;
  --el-bg-color-page: #fafbfc;
  --el-bg-color-overlay: #ffffff;
  
  /* 文字色彩 */
  --el-text-color-primary: #1a1a1a;
  --el-text-color-regular: #4b5563;
  --el-text-color-secondary: #6b7280;
  --el-text-color-placeholder: #9ca3af;
  
  /* 边框色彩 */
  --el-border-color: #e5e7eb;
  --el-border-color-light: #f3f4f6;
  --el-border-color-lighter: #f9fafb;
  --el-border-color-extra-light: #fafbfc;
  
  /* 填充色彩 */
  --el-fill-color: #f3f4f6;
  --el-fill-color-light: #f9fafb;
  --el-fill-color-lighter: #fafbfc;
  --el-fill-color-extra-light: #fefefe;
  
  /* 阴影 */
  --el-box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --el-box-shadow-light: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --el-box-shadow-lighter: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  
  /* 圆角 */
  --el-border-radius-base: 8px;
  --el-border-radius-small: 6px;
  --el-border-radius-round: 20px;
  --el-border-radius-circle: 50%;
}

/* 现代卡片样式 */
.el-card {
  border: 1px solid var(--el-border-color-light);
  border-radius: 12px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  background-color: #ffffff;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.el-card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transform: translateY(-1px);
}

.el-card__header {
  background-color: #ffffff;
  border-bottom: 1px solid var(--el-border-color-light);
  padding: 20px 24px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.el-card__body {
  padding: 24px;
}

/* 现代按钮样式 */
.el-button {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid transparent;
  font-size: 14px;
  padding: 10px 16px;
}

.el-button--primary {
  background: linear-gradient(135deg, var(--el-color-primary) 0%, var(--el-color-primary-light-3) 100%);
  border-color: var(--el-color-primary);
  color: #ffffff;
  box-shadow: 0 1px 2px 0 rgba(37, 99, 235, 0.2);
}

.el-button--primary:hover {
  background: linear-gradient(135deg, var(--el-color-primary-dark-2) 0%, var(--el-color-primary) 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px 0 rgba(37, 99, 235, 0.3);
}

.el-button--success {
  background: linear-gradient(135deg, var(--el-color-success) 0%, #34d399 100%);
  border-color: var(--el-color-success);
  color: #ffffff;
  box-shadow: 0 1px 2px 0 rgba(16, 185, 129, 0.2);
}

.el-button--success:hover {
  background: linear-gradient(135deg, #059669 0%, var(--el-color-success) 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px 0 rgba(16, 185, 129, 0.3);
}

.el-button--warning {
  background: linear-gradient(135deg, var(--el-color-warning) 0%, #fbbf24 100%);
  border-color: var(--el-color-warning);
  color: #ffffff;
  box-shadow: 0 1px 2px 0 rgba(245, 158, 11, 0.2);
}

.el-button--warning:hover {
  background: linear-gradient(135deg, #d97706 0%, var(--el-color-warning) 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px 0 rgba(245, 158, 11, 0.3);
}

.el-button--danger {
  background: linear-gradient(135deg, var(--el-color-danger) 0%, #f87171 100%);
  border-color: var(--el-color-danger);
  color: #ffffff;
  box-shadow: 0 1px 2px 0 rgba(239, 68, 68, 0.2);
}

.el-button--danger:hover {
  background: linear-gradient(135deg, #dc2626 0%, var(--el-color-danger) 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px 0 rgba(239, 68, 68, 0.3);
}

.el-button.is-plain {
  background-color: #ffffff;
  border-color: var(--el-border-color);
  color: var(--el-text-color-primary);
}

.el-button.is-plain:hover {
  background-color: var(--el-fill-color-light);
  border-color: var(--el-color-primary);
  color: var(--el-color-primary);
  transform: translateY(-1px);
}

.el-button.is-text {
  background: transparent;
  border: none;
  color: var(--el-color-primary);
  padding: 8px 12px;
}

.el-button.is-text:hover {
  background-color: var(--el-color-primary-light-9);
  color: var(--el-color-primary-dark-2);
}

/* 表格样式 */
.el-table {
  background-color: #ffffff;
  color: #333333;
}

.el-table th.el-table__cell {
  background-color: #fafafa;
  color: #333333;
  border-bottom: 1px solid #e5e5e5;
}

.el-table td.el-table__cell {
  border-bottom: 1px solid #f0f0f0;
}

.el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell {
  background-color: #fafafa;
}

/* 标签样式 */
.el-tag {
  border: 1px solid #e5e5e5;
  background-color: #f5f5f5;
  color: #333333;
}

.el-tag--success {
  background-color: #000000;
  border-color: #000000;
  color: #ffffff;
}

.el-tag--warning {
  background-color: #666666;
  border-color: #666666;
  color: #ffffff;
}

.el-tag--danger {
  background-color: #999999;
  border-color: #999999;
  color: #ffffff;
}

.el-tag--info {
  background-color: #cccccc;
  border-color: #cccccc;
  color: #333333;
}

/* 输入框样式 */
.el-input__wrapper {
  background-color: #ffffff;
  border: 1px solid #e5e5e5;
}

.el-input__wrapper:hover {
  border-color: #cccccc;
}

.el-input__wrapper.is-focus {
  border-color: #000000;
  box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1);
}

/* 选择器样式 */
.el-select .el-input__wrapper {
  background-color: #ffffff;
}

.el-select-dropdown {
  background-color: #ffffff;
  border: 1px solid #e5e5e5;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 菜单样式 */
.el-menu {
  background-color: #ffffff;
  border-right: 1px solid #e5e5e5;
}

.el-menu-item {
  color: #666666;
}

.el-menu-item:hover {
  background-color: #f5f5f5;
  color: #000000;
}

.el-menu-item.is-active {
  background-color: #000000;
  color: #ffffff;
}

/* 分页样式 */
.el-pagination {
  color: #333333;
}

.el-pagination .el-pager li {
  background-color: #ffffff;
  color: #333333;
  border: 1px solid #e5e5e5;
}

.el-pagination .el-pager li:hover {
  background-color: #f5f5f5;
}

.el-pagination .el-pager li.is-active {
  background-color: #000000;
  color: #ffffff;
  border-color: #000000;
}

/* 对话框样式 */
.el-dialog {
  background-color: #ffffff;
  border: 1px solid #e5e5e5;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.el-dialog__header {
  background-color: #fafafa;
  border-bottom: 1px solid #e5e5e5;
}

.el-dialog__title {
  color: #333333;
}

/* 进度条样式 */
.el-progress-bar__outer {
  background-color: #f0f0f0;
}

.el-progress-bar__inner {
  background-color: #000000;
}

/* 评分样式 */
.el-rate__icon {
  color: #e5e5e5;
}

.el-rate__icon.is-active {
  color: #000000;
}

/* 时间线样式 */
.el-timeline-item__node {
  background-color: #000000;
  border-color: #000000;
}

.el-timeline-item__wrapper {
  color: #333333;
}

/* 步骤条样式 */
.el-steps .el-step__head.is-process {
  color: #000000;
  border-color: #000000;
}

.el-steps .el-step__head.is-finish {
  color: #000000;
  border-color: #000000;
}

.el-steps .el-step__head.is-finish .el-step__icon {
  background-color: #000000;
  color: #ffffff;
}

.el-steps .el-step__head.is-process .el-step__icon {
  background-color: #000000;
  color: #ffffff;
}

/* 描述列表样式 */
.el-descriptions {
  background-color: #ffffff;
}

.el-descriptions__header {
  background-color: #fafafa;
}

.el-descriptions__title {
  color: #333333;
}

.el-descriptions__body .el-descriptions__table .el-descriptions__cell {
  border: 1px solid #e5e5e5;
}

.el-descriptions__label {
  background-color: #fafafa;
  color: #666666;
}

.el-descriptions__content {
  background-color: #ffffff;
  color: #333333;
}

/* 空状态样式 */
.el-empty {
  color: #999999;
}

.el-empty__description {
  color: #cccccc;
}

/* 统计数字样式 */
.el-statistic__content {
  color: #333333;
}

.el-statistic__number {
  color: #000000;
}

/* 开关样式 */
.el-switch.is-checked .el-switch__core {
  background-color: #000000;
  border-color: #000000;
}

/* 滑块样式 */
.el-slider__runway {
  background-color: #e5e5e5;
}

.el-slider__bar {
  background-color: #000000;
}

.el-slider__button {
  border-color: #000000;
}

/* 上传组件样式 */
.el-upload-dragger {
  background-color: #fafafa;
  border: 2px dashed #e5e5e5;
}

.el-upload-dragger:hover {
  border-color: #000000;
}

/* 图片预览样式 */
.el-image-viewer__wrapper {
  background-color: rgba(0, 0, 0, 0.8);
}

/* 消息提示样式 */
.el-message {
  background-color: #ffffff;
  border: 1px solid #e5e5e5;
  color: #333333;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.el-message--success {
  background-color: #000000;
  border-color: #000000;
  color: #ffffff;
}

.el-message--warning {
  background-color: #666666;
  border-color: #666666;
  color: #ffffff;
}

.el-message--error {
  background-color: #999999;
  border-color: #999999;
  color: #ffffff;
}

/* 通知样式 */
.el-notification {
  background-color: #ffffff;
  border: 1px solid #e5e5e5;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

/* 抽屉样式 */
.el-drawer {
  background-color: #ffffff;
}

.el-drawer__header {
  background-color: #fafafa;
  border-bottom: 1px solid #e5e5e5;
}

/* 折叠面板样式 */
.el-collapse {
  background-color: #ffffff;
  border: 1px solid #e5e5e5;
}

.el-collapse-item__header {
  background-color: #fafafa;
  color: #333333;
  border-bottom: 1px solid #e5e5e5;
}

.el-collapse-item__content {
  background-color: #ffffff;
  color: #333333;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background-color: #f5f5f5;
}

::-webkit-scrollbar-thumb {
  background-color: #cccccc;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: #999999;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .el-card__body {
    padding: 15px;
  }
  
  .el-table {
    font-size: 14px;
  }
  
  .el-button {
    padding: 8px 15px;
    font-size: 14px;
  }
}
/* 现代化
增强样式 */

/* 平滑滚动 */
html {
  scroll-behavior: smooth;
}

/* 现代化滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
  transition: background 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 现代化选择样式 */
::selection {
  background: rgba(37, 99, 235, 0.2);
  color: #1a1a1a;
}

/* 现代化焦点样式 */
*:focus {
  outline: 2px solid rgba(37, 99, 235, 0.5);
  outline-offset: 2px;
}

/* 现代化动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 现代化工具类 */
.fade-in {
  animation: fadeIn 0.3s ease-out;
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

.pulse {
  animation: pulse 2s infinite;
}

.glass-effect {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.shadow-soft {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.shadow-medium {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.shadow-strong {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.gradient-text {
  background: linear-gradient(135deg, #2563eb 0%, #7c3aed 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.rounded-modern {
  border-radius: 12px;
}

.rounded-soft {
  border-radius: 8px;
}

.rounded-full {
  border-radius: 50%;
}

/* 现代化间距 */
.space-y-2 > * + * {
  margin-top: 8px;
}

.space-y-4 > * + * {
  margin-top: 16px;
}

.space-y-6 > * + * {
  margin-top: 24px;
}

.space-x-2 > * + * {
  margin-left: 8px;
}

.space-x-4 > * + * {
  margin-left: 16px;
}

/* 现代化文本样式 */
.text-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-primary {
  color: var(--el-color-primary);
}

.text-success {
  color: var(--el-color-success);
}

.text-warning {
  color: var(--el-color-warning);
}

.text-danger {
  color: var(--el-color-danger);
}

.text-muted {
  color: var(--el-text-color-secondary);
}

/* 现代化背景 */
.bg-gradient-primary {
  background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
}

.bg-gradient-success {
  background: linear-gradient(135deg, #10b981 0%, #34d399 100%);
}

.bg-gradient-warning {
  background: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%);
}

.bg-gradient-danger {
  background: linear-gradient(135deg, #ef4444 0%, #f87171 100%);
}

/* 现代化边框 */
.border-modern {
  border: 1px solid var(--el-border-color-light);
}

.border-primary {
  border: 1px solid var(--el-color-primary);
}

.border-success {
  border: 1px solid var(--el-color-success);
}

.border-warning {
  border: 1px solid var(--el-color-warning);
}

.border-danger {
  border: 1px solid var(--el-color-danger);
}

/* 响应式工具类 */
@media (max-width: 768px) {
  .mobile-hidden {
    display: none !important;
  }
  
  .mobile-full {
    width: 100% !important;
  }
}

@media (min-width: 769px) {
  .desktop-hidden {
    display: none !important;
  }
}

/* 现代化加载状态 */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 现代化状态指示器 */
.status-dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
}

.status-dot.online {
  background-color: #10b981;
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
}

.status-dot.offline {
  background-color: #6b7280;
}

.status-dot.busy {
  background-color: #f59e0b;
  box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.2);
}

.status-dot.error {
  background-color: #ef4444;
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
}

/* 现代化徽章 */
.badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  line-height: 1;
}

.badge.primary {
  background-color: #dbeafe;
  color: #2563eb;
}

.badge.success {
  background-color: #d1fae5;
  color: #10b981;
}

.badge.warning {
  background-color: #fef3c7;
  color: #f59e0b;
}

.badge.danger {
  background-color: #fee2e2;
  color: #ef4444;
}

.badge.info {
  background-color: #f3f4f6;
  color: #6b7280;
}